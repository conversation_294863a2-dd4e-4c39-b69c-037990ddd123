{"version": 3, "sources": [], "sections": [{"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/JummahNext/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/JummahNext/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/JummahNext/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Repos/JummahNext/src/app/page.tsx"], "sourcesContent": ["import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b\">\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold text-primary\">Jummah Management System</h1>\n          <div className=\"flex items-center gap-4\">\n            <SignedIn>\n              <UserButton />\n            </SignedIn>\n            <SignedOut>\n              <SignInButton>\n                <Button>Sign In</Button>\n              </SignInButton>\n            </SignedOut>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <SignedOut>\n          <div className=\"text-center py-16\">\n            <h2 className=\"text-4xl font-bold mb-4\">Welcome to Jummah Management System</h2>\n            <p className=\"text-xl text-muted-foreground mb-8\">\n              A comprehensive system to manage Jummah scheduling, branches, and dhae assignments\n            </p>\n            <SignInButton>\n              <Button size=\"lg\">Get Started</Button>\n            </SignInButton>\n          </div>\n        </SignedOut>\n\n        <SignedIn>\n          <div className=\"space-y-8\">\n            <div>\n              <h2 className=\"text-3xl font-bold mb-2\">Dashboard</h2>\n              <p className=\"text-muted-foreground\">\n                Welcome back! Here's an overview of your Jummah management system.\n              </p>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Total Branches</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">12</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    +2 from last month\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Active Dhae</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">24</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    +1 from last month\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">This Week's Schedules</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">8</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    All confirmed\n                  </p>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Completion Rate</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">98%</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    +2% from last month\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Quick Actions */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Quick Actions</CardTitle>\n                <CardDescription>\n                  Common tasks to get you started\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <Button className=\"h-auto p-4 flex flex-col items-start\">\n                    <span className=\"font-semibold\">Schedule Jummah</span>\n                    <span className=\"text-sm text-muted-foreground\">Create new schedule</span>\n                  </Button>\n                  <Button variant=\"outline\" className=\"h-auto p-4 flex flex-col items-start\">\n                    <span className=\"font-semibold\">Manage Branches</span>\n                    <span className=\"text-sm text-muted-foreground\">Add or edit branches</span>\n                  </Button>\n                  <Button variant=\"outline\" className=\"h-auto p-4 flex flex-col items-start\">\n                    <span className=\"font-semibold\">Manage Dhae</span>\n                    <span className=\"text-sm text-muted-foreground\">Add or edit dhae profiles</span>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </SignedIn>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkC;;;;;;sCAChD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yKAAA,CAAA,WAAQ;8CACP,cAAA,8OAAC,sLAAA,CAAA,aAAU;;;;;;;;;;8CAEb,8OAAC,yKAAA,CAAA,YAAS;8CACR,cAAA,8OAAC,sLAAA,CAAA,eAAY;kDACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,yKAAA,CAAA,YAAS;kCACR,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC,sLAAA,CAAA,eAAY;8CACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;kCAKxB,8OAAC,yKAAA,CAAA,WAAQ;kCACP,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAE7C,8OAAC,gIAAA,CAAA,cAAW;;sEACV,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAMjD,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAE7C,8OAAC,gIAAA,CAAA,cAAW;;sEACV,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAMjD,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAE7C,8OAAC,gIAAA,CAAA,cAAW;;sEACV,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;sDAMjD,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB;;;;;;;;;;;8DAE7C,8OAAC,gIAAA,CAAA,cAAW;;sEACV,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;8CAQnD,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;kEAElD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;kEAElD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;;0EAClC,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,8OAAC;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpE", "debugId": null}}]}