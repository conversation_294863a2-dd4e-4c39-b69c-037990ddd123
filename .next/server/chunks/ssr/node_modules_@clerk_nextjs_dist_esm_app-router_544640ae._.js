module.exports = {

"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@clerk_nextjs_dist_esm_app-router_4f747d08._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-creator-reader.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@clerk_nextjs_dist_esm_app-router_7e4e4af3._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-ssr] (ecmascript)");
    });
});
}}),

};