import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/types/database'
import { ScheduleFormData, ScheduleFilter } from '@/lib/validations/schemas'
import { mockScheduleData, getMockScheduleStats } from './mock-data'

type Schedule = Database['public']['Tables']['jummah_schedules']['Row']
type ScheduleInsert = Database['public']['Tables']['jummah_schedules']['Insert']
type ScheduleUpdate = Database['public']['Tables']['jummah_schedules']['Update']

export class ScheduleService {
  static async getAll(organizationId: string, filter?: ScheduleFilter) {
    let query = supabase
      .from('jummah_schedules')
      .select(`
        *,
        branches (
          id,
          branch_name,
          branch_code,
          district
        ),
        dhae (
          id,
          dhae_name,
          dhae_contact,
          rating
        )
      `)
      .eq('organization_id', organizationId)
      .order('schedule_date', { ascending: true })

    // Apply filters
    if (filter?.search) {
      query = query.or(`branches.branch_name.ilike.%${filter.search}%,dhae.dhae_name.ilike.%${filter.search}%`)
    }

    if (filter?.status) {
      query = query.eq('status', filter.status)
    }

    if (filter?.priority) {
      query = query.eq('priority', filter.priority)
    }

    if (filter?.branch_id) {
      query = query.eq('branch_id', filter.branch_id)
    }

    if (filter?.dhae_id) {
      query = query.eq('dhae_id', filter.dhae_id)
    }

    if (filter?.date_from) {
      query = query.gte('schedule_date', filter.date_from)
    }

    if (filter?.date_to) {
      query = query.lte('schedule_date', filter.date_to)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch schedules: ${error.message}`)
    }

    return data
  }

  static async getById(id: string, organizationId: string) {
    const { data, error } = await supabase
      .from('jummah_schedules')
      .select(`
        *,
        branches (
          id,
          branch_name,
          branch_code,
          district,
          capacity
        ),
        dhae (
          id,
          dhae_name,
          dhae_contact,
          dhae_email,
          rating
        )
      `)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .single()

    if (error) {
      throw new Error(`Failed to fetch schedule: ${error.message}`)
    }

    return data
  }

  static async create(organizationId: string, data: ScheduleFormData, createdBy: string) {
    const scheduleData: ScheduleInsert = {
      ...data,
      organization_id: organizationId,
      created_by: createdBy,
    }

    const { data: schedule, error } = await supabase
      .from('jummah_schedules')
      .insert(scheduleData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create schedule: ${error.message}`)
    }

    // Log the creation in history
    await this.logHistory(schedule.id, 'created', {}, createdBy, 'Schedule created')

    return schedule
  }

  static async update(id: string, organizationId: string, data: Partial<ScheduleFormData>, updatedBy: string) {
    // Get current data for history
    const currentSchedule = await this.getById(id, organizationId)

    const updateData: ScheduleUpdate = {
      ...data,
      updated_at: new Date().toISOString(),
    }

    const { data: schedule, error } = await supabase
      .from('jummah_schedules')
      .update(updateData)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update schedule: ${error.message}`)
    }

    // Log the update in history
    await this.logHistory(id, 'updated', { 
      from: currentSchedule, 
      to: data 
    }, updatedBy, 'Schedule updated')

    return schedule
  }

  static async delete(id: string, organizationId: string, deletedBy: string, reason?: string) {
    const { error } = await supabase
      .from('jummah_schedules')
      .delete()
      .eq('id', id)
      .eq('organization_id', organizationId)

    if (error) {
      throw new Error(`Failed to delete schedule: ${error.message}`)
    }

    // Log the deletion in history
    await this.logHistory(id, 'deleted', {}, deletedBy, reason || 'Schedule deleted')

    return true
  }

  static async getUpcoming(organizationId: string, limit: number = 10) {
    // If using mock organization, return mock data
    if (organizationId === 'mock-org-id') {
      const today = new Date().toISOString().split('T')[0]
      return mockScheduleData
        .filter(s => s.schedule_date >= today && s.status !== 'cancelled')
        .sort((a, b) => a.schedule_date.localeCompare(b.schedule_date))
        .slice(0, limit)
    }

    const today = new Date().toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('jummah_schedules')
      .select(`
        *,
        branches (
          id,
          branch_name,
          branch_code
        ),
        dhae (
          id,
          dhae_name
        )
      `)
      .eq('organization_id', organizationId)
      .gte('schedule_date', today)
      .neq('status', 'cancelled')
      .order('schedule_date', { ascending: true })
      .limit(limit)

    if (error) {
      // If database not set up, return mock data
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        console.warn('Database not set up, returning mock schedule data')
        return mockScheduleData
          .filter(s => s.schedule_date >= today && s.status !== 'cancelled')
          .sort((a, b) => a.schedule_date.localeCompare(b.schedule_date))
          .slice(0, limit)
      }
      throw new Error(`Failed to fetch upcoming schedules: ${error.message}`)
    }

    return data
  }

  static async getStats(organizationId: string) {
    // If using mock organization, return mock stats
    if (organizationId === 'mock-org-id') {
      return getMockScheduleStats()
    }

    const today = new Date().toISOString().split('T')[0]
    const weekFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const { data, error } = await supabase
      .from('jummah_schedules')
      .select('status, priority, estimated_attendance, actual_attendance, schedule_date, dhae_id')
      .eq('organization_id', organizationId)

    if (error) {
      // If database not set up, return mock stats
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        console.warn('Database not set up, returning mock schedule stats')
        return getMockScheduleStats()
      }
      throw new Error(`Failed to fetch schedule stats: ${error.message}`)
    }

    const upcoming = data.filter(s => s.schedule_date >= today)
    const thisWeek = data.filter(s => s.schedule_date >= today && s.schedule_date <= weekFromNow)

    const stats = {
      total: data.length,
      upcoming: upcoming.length,
      thisWeek: thisWeek.length,
      confirmed: upcoming.filter(s => s.status === 'confirmed').length,
      pending: upcoming.filter(s => s.status === 'scheduled').length,
      unassigned: upcoming.filter(s => !s.dhae_id).length,
      totalExpectedAttendance: thisWeek.reduce((sum, s) => sum + (s.estimated_attendance || 0), 0),
      completionRate: data.length > 0
        ? Math.round((data.filter(s => s.status === 'completed').length / data.length) * 100)
        : 0,
    }

    return stats
  }

  static async checkConflicts(organizationId: string, dhaeId: string, date: string, excludeScheduleId?: string) {
    let query = supabase
      .from('jummah_schedules')
      .select('id, branch_id, branches(branch_name)')
      .eq('organization_id', organizationId)
      .eq('dhae_id', dhaeId)
      .eq('schedule_date', date)
      .neq('status', 'cancelled')

    if (excludeScheduleId) {
      query = query.neq('id', excludeScheduleId)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to check conflicts: ${error.message}`)
    }

    return data || []
  }

  static async approve(id: string, organizationId: string, approvedBy: string) {
    const { data, error } = await supabase
      .from('jummah_schedules')
      .update({
        status: 'confirmed',
        approved_by: approvedBy,
        approved_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('organization_id', organizationId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to approve schedule: ${error.message}`)
    }

    // Log the approval in history
    await this.logHistory(id, 'approved', {}, approvedBy, 'Schedule approved')

    return data
  }

  private static async logHistory(
    scheduleId: string, 
    action: string, 
    changes: any, 
    changedBy: string, 
    reason?: string
  ) {
    const { error } = await supabase
      .from('schedule_history')
      .insert({
        schedule_id: scheduleId,
        action,
        changes,
        changed_by: changedBy,
        reason
      })

    if (error) {
      console.error('Failed to log schedule history:', error)
    }
  }
}
