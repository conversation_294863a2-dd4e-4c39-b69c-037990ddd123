import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/types/database'
import { BranchFormData, BranchFilter } from '@/lib/validations/schemas'

type Branch = Database['public']['Tables']['branches']['Row']
type BranchInsert = Database['public']['Tables']['branches']['Insert']
type BranchUpdate = Database['public']['Tables']['branches']['Update']

export class BranchService {
  static async getAll(organizationId: string, filter?: BranchFilter) {
    // If using mock organization, return mock data
    if (organizationId === 'mock-org-id') {
      return this.getMockBranches(filter)
    }

    let query = supabase
      .from('branches')
      .select(`
        *,
        cities (
          id,
          name,
          state
        )
      `)
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })

    // Apply filters
    if (filter?.search) {
      query = query.or(`branch_name.ilike.%${filter.search}%,branch_code.ilike.%${filter.search}%,district.ilike.%${filter.search}%`)
    }

    if (filter?.status) {
      query = query.eq('status', filter.status)
    }

    if (filter?.city_id) {
      query = query.eq('city_id', filter.city_id)
    }

    if (filter?.capacity_min) {
      query = query.gte('capacity', filter.capacity_min)
    }

    if (filter?.capacity_max) {
      query = query.lte('capacity', filter.capacity_max)
    }

    const { data, error } = await query

    if (error) {
      // If database not set up, return mock data
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        console.warn('Database not set up, returning mock branches data')
        return this.getMockBranches(filter)
      }
      throw new Error(`Failed to fetch branches: ${error.message}`)
    }

    return data
  }

  private static getMockBranches(filter?: BranchFilter) {
    const mockBranches = [
      {
        id: '1',
        organization_id: 'mock-org-id',
        branch_code: 'MAN001',
        branch_name: 'Masjid Al-Noor',
        jip_name: 'Ahmad bin Abdullah',
        jip_contact: '+60123456789',
        jip_email: '<EMAIL>',
        address_line1: '123 Jalan Masjid',
        address_line2: null,
        city_id: '1',
        district: 'Wangsa Maju',
        postal_code: '53300',
        latitude: null,
        longitude: null,
        capacity: 500,
        facilities: ['Parking', 'Air Conditioning', 'Sound System'],
        prayer_times: null,
        contact_person: null,
        status: 'active' as const,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        is_deleted: false,
        cities: {
          id: '1',
          name: 'Kuala Lumpur',
          state: 'Federal Territory'
        }
      },
      {
        id: '2',
        organization_id: 'mock-org-id',
        branch_code: 'IC002',
        branch_name: 'Islamic Center KL',
        jip_name: 'Mohammed bin Hassan',
        jip_contact: '+60123456790',
        jip_email: '<EMAIL>',
        address_line1: '456 Jalan Pusat Islam',
        address_line2: null,
        city_id: '1',
        district: 'KLCC',
        postal_code: '50088',
        latitude: null,
        longitude: null,
        capacity: 800,
        facilities: ['Parking', 'Air Conditioning', 'Sound System', 'Library'],
        prayer_times: null,
        contact_person: null,
        status: 'active' as const,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        is_deleted: false,
        cities: {
          id: '1',
          name: 'Kuala Lumpur',
          state: 'Federal Territory'
        }
      },
      {
        id: '3',
        organization_id: 'mock-org-id',
        branch_code: 'CM003',
        branch_name: 'Community Mosque',
        jip_name: 'Ali bin Omar',
        jip_contact: '+60123456791',
        jip_email: '<EMAIL>',
        address_line1: '789 Jalan Komuniti',
        address_line2: null,
        city_id: '2',
        district: 'Shah Alam',
        postal_code: '40000',
        latitude: null,
        longitude: null,
        capacity: 300,
        facilities: ['Parking', 'Sound System'],
        prayer_times: null,
        contact_person: null,
        status: 'active' as const,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        is_deleted: false,
        cities: {
          id: '2',
          name: 'Shah Alam',
          state: 'Selangor'
        }
      }
    ]

    // Apply filters
    let filtered = mockBranches

    if (filter?.search) {
      const search = filter.search.toLowerCase()
      filtered = filtered.filter(branch =>
        branch.branch_name.toLowerCase().includes(search) ||
        branch.branch_code.toLowerCase().includes(search) ||
        branch.district?.toLowerCase().includes(search)
      )
    }

    if (filter?.status) {
      filtered = filtered.filter(branch => branch.status === filter.status)
    }

    return filtered
  }

  static async getById(id: string, organizationId: string) {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        cities (
          id,
          name,
          state
        )
      `)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)
      .single()

    if (error) {
      throw new Error(`Failed to fetch branch: ${error.message}`)
    }

    return data
  }

  static async create(organizationId: string, data: BranchFormData) {
    const branchData: BranchInsert = {
      ...data,
      organization_id: organizationId,
    }

    const { data: branch, error } = await supabase
      .from('branches')
      .insert(branchData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create branch: ${error.message}`)
    }

    return branch
  }

  static async update(id: string, organizationId: string, data: Partial<BranchFormData>) {
    const updateData: BranchUpdate = {
      ...data,
      updated_at: new Date().toISOString(),
    }

    const { data: branch, error } = await supabase
      .from('branches')
      .update(updateData)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update branch: ${error.message}`)
    }

    return branch
  }

  static async delete(id: string, organizationId: string) {
    const { error } = await supabase
      .from('branches')
      .update({ 
        is_deleted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('organization_id', organizationId)

    if (error) {
      throw new Error(`Failed to delete branch: ${error.message}`)
    }

    return true
  }

  static async getStats(organizationId: string) {
    // If using mock organization, return mock stats
    if (organizationId === 'mock-org-id') {
      const mockBranches = this.getMockBranches()
      return {
        total: mockBranches.length,
        active: mockBranches.filter(b => b.status === 'active').length,
        inactive: mockBranches.filter(b => b.status === 'inactive').length,
        maintenance: mockBranches.filter(b => b.status === 'maintenance').length,
        totalCapacity: mockBranches.reduce((sum, b) => sum + (b.capacity || 0), 0),
        averageCapacity: mockBranches.length > 0
          ? Math.round(mockBranches.reduce((sum, b) => sum + (b.capacity || 0), 0) / mockBranches.length)
          : 0,
      }
    }

    const { data, error } = await supabase
      .from('branches')
      .select('status, capacity')
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)

    if (error) {
      // If database not set up, return mock stats
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        console.warn('Database not set up, returning mock branch stats')
        const mockBranches = this.getMockBranches()
        return {
          total: mockBranches.length,
          active: mockBranches.filter(b => b.status === 'active').length,
          inactive: mockBranches.filter(b => b.status === 'inactive').length,
          maintenance: mockBranches.filter(b => b.status === 'maintenance').length,
          totalCapacity: mockBranches.reduce((sum, b) => sum + (b.capacity || 0), 0),
          averageCapacity: mockBranches.length > 0
            ? Math.round(mockBranches.reduce((sum, b) => sum + (b.capacity || 0), 0) / mockBranches.length)
            : 0,
        }
      }
      throw new Error(`Failed to fetch branch stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: data.filter(b => b.status === 'active').length,
      inactive: data.filter(b => b.status === 'inactive').length,
      maintenance: data.filter(b => b.status === 'maintenance').length,
      totalCapacity: data.reduce((sum, b) => sum + (b.capacity || 0), 0),
      averageCapacity: data.length > 0
        ? Math.round(data.reduce((sum, b) => sum + (b.capacity || 0), 0) / data.length)
        : 0,
    }

    return stats
  }

  static async checkCodeExists(organizationId: string, branchCode: string, excludeId?: string) {
    let query = supabase
      .from('branches')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('branch_code', branchCode)
      .eq('is_deleted', false)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to check branch code: ${error.message}`)
    }

    return data.length > 0
  }
}
