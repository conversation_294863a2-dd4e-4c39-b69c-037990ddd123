import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/types/database'
import { BranchFormData, BranchFilter } from '@/lib/validations/schemas'

type Branch = Database['public']['Tables']['branches']['Row']
type BranchInsert = Database['public']['Tables']['branches']['Insert']
type BranchUpdate = Database['public']['Tables']['branches']['Update']

export class BranchService {
  static async getAll(organizationId: string, filter?: BranchFilter) {
    let query = supabase
      .from('branches')
      .select(`
        *,
        cities (
          id,
          name,
          state
        )
      `)
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })

    // Apply filters
    if (filter?.search) {
      query = query.or(`branch_name.ilike.%${filter.search}%,branch_code.ilike.%${filter.search}%,district.ilike.%${filter.search}%`)
    }

    if (filter?.status) {
      query = query.eq('status', filter.status)
    }

    if (filter?.city_id) {
      query = query.eq('city_id', filter.city_id)
    }

    if (filter?.capacity_min) {
      query = query.gte('capacity', filter.capacity_min)
    }

    if (filter?.capacity_max) {
      query = query.lte('capacity', filter.capacity_max)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch branches: ${error.message}`)
    }

    return data
  }

  static async getById(id: string, organizationId: string) {
    const { data, error } = await supabase
      .from('branches')
      .select(`
        *,
        cities (
          id,
          name,
          state
        )
      `)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)
      .single()

    if (error) {
      throw new Error(`Failed to fetch branch: ${error.message}`)
    }

    return data
  }

  static async create(organizationId: string, data: BranchFormData) {
    const branchData: BranchInsert = {
      ...data,
      organization_id: organizationId,
    }

    const { data: branch, error } = await supabase
      .from('branches')
      .insert(branchData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create branch: ${error.message}`)
    }

    return branch
  }

  static async update(id: string, organizationId: string, data: Partial<BranchFormData>) {
    const updateData: BranchUpdate = {
      ...data,
      updated_at: new Date().toISOString(),
    }

    const { data: branch, error } = await supabase
      .from('branches')
      .update(updateData)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update branch: ${error.message}`)
    }

    return branch
  }

  static async delete(id: string, organizationId: string) {
    const { error } = await supabase
      .from('branches')
      .update({ 
        is_deleted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('organization_id', organizationId)

    if (error) {
      throw new Error(`Failed to delete branch: ${error.message}`)
    }

    return true
  }

  static async getStats(organizationId: string) {
    const { data, error } = await supabase
      .from('branches')
      .select('status, capacity')
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)

    if (error) {
      throw new Error(`Failed to fetch branch stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: data.filter(b => b.status === 'active').length,
      inactive: data.filter(b => b.status === 'inactive').length,
      maintenance: data.filter(b => b.status === 'maintenance').length,
      totalCapacity: data.reduce((sum, b) => sum + (b.capacity || 0), 0),
      averageCapacity: data.length > 0 
        ? Math.round(data.reduce((sum, b) => sum + (b.capacity || 0), 0) / data.length)
        : 0,
    }

    return stats
  }

  static async checkCodeExists(organizationId: string, branchCode: string, excludeId?: string) {
    let query = supabase
      .from('branches')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('branch_code', branchCode)
      .eq('is_deleted', false)

    if (excludeId) {
      query = query.neq('id', excludeId)
    }

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to check branch code: ${error.message}`)
    }

    return data.length > 0
  }
}
