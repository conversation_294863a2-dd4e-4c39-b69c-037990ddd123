import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/types/database'
import { DhaeFormData, DhaeFilter } from '@/lib/validations/schemas'
import { mockDhaeData, getMockDhaeStats } from './mock-data'

type Dhae = Database['public']['Tables']['dhae']['Row']
type DhaeInsert = Database['public']['Tables']['dhae']['Insert']
type DhaeUpdate = Database['public']['Tables']['dhae']['Update']

export class DhaeService {
  static async getAll(organizationId: string, filter?: DhaeFilter) {
    // If using mock organization, return mock data
    if (organizationId === 'mock-org-id') {
      return this.getMockDhae(filter)
    }

    let query = supabase
      .from('dhae')
      .select(`
        *,
        cities (
          id,
          name,
          state
        )
      `)
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })

    // Apply filters
    if (filter?.search) {
      query = query.or(`dhae_name.ilike.%${filter.search}%,dhae_email.ilike.%${filter.search}%,district.ilike.%${filter.search}%`)
    }

    if (filter?.status) {
      query = query.eq('status', filter.status)
    }

    if (filter?.city_id) {
      query = query.eq('city_id', filter.city_id)
    }

    if (filter?.experience_min) {
      query = query.gte('experience_years', filter.experience_min)
    }

    if (filter?.rating_min) {
      query = query.gte('rating', filter.rating_min)
    }

    if (filter?.specializations && filter.specializations.length > 0) {
      query = query.overlaps('specializations', filter.specializations)
    }

    if (filter?.languages && filter.languages.length > 0) {
      query = query.overlaps('languages', filter.languages)
    }

    const { data, error } = await query

    if (error) {
      // If database not set up, return mock data
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        console.warn('Database not set up, returning mock dhae data')
        return this.getMockDhae(filter)
      }
      throw new Error(`Failed to fetch dhae: ${error.message}`)
    }

    return data
  }

  private static getMockDhae(filter?: DhaeFilter) {
    let filtered = [...mockDhaeData]

    if (filter?.search) {
      const search = filter.search.toLowerCase()
      filtered = filtered.filter(dhae =>
        dhae.dhae_name.toLowerCase().includes(search) ||
        dhae.dhae_email?.toLowerCase().includes(search) ||
        dhae.district?.toLowerCase().includes(search)
      )
    }

    if (filter?.status) {
      filtered = filtered.filter(dhae => dhae.status === filter.status)
    }

    if (filter?.experience_min) {
      filtered = filtered.filter(dhae => dhae.experience_years >= filter.experience_min!)
    }

    if (filter?.rating_min) {
      filtered = filtered.filter(dhae => dhae.rating >= filter.rating_min!)
    }

    return filtered
  }

  static async getById(id: string, organizationId: string) {
    const { data, error } = await supabase
      .from('dhae')
      .select(`
        *,
        cities (
          id,
          name,
          state
        )
      `)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)
      .single()

    if (error) {
      throw new Error(`Failed to fetch dhae: ${error.message}`)
    }

    return data
  }

  static async create(organizationId: string, data: DhaeFormData) {
    const dhaeData: DhaeInsert = {
      ...data,
      organization_id: organizationId,
    }

    const { data: dhae, error } = await supabase
      .from('dhae')
      .insert(dhaeData)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create dhae: ${error.message}`)
    }

    return dhae
  }

  static async update(id: string, organizationId: string, data: Partial<DhaeFormData>) {
    const updateData: DhaeUpdate = {
      ...data,
      updated_at: new Date().toISOString(),
    }

    const { data: dhae, error } = await supabase
      .from('dhae')
      .update(updateData)
      .eq('id', id)
      .eq('organization_id', organizationId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update dhae: ${error.message}`)
    }

    return dhae
  }

  static async delete(id: string, organizationId: string) {
    const { error } = await supabase
      .from('dhae')
      .update({ 
        is_deleted: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('organization_id', organizationId)

    if (error) {
      throw new Error(`Failed to delete dhae: ${error.message}`)
    }

    return true
  }

  static async getStats(organizationId: string) {
    // If using mock organization, return mock stats
    if (organizationId === 'mock-org-id') {
      return getMockDhaeStats()
    }

    const { data, error } = await supabase
      .from('dhae')
      .select('status, rating, total_assignments, experience_years')
      .eq('organization_id', organizationId)
      .eq('is_deleted', false)

    if (error) {
      // If database not set up, return mock stats
      if (error.message.includes('relation') || error.message.includes('does not exist')) {
        console.warn('Database not set up, returning mock dhae stats')
        return getMockDhaeStats()
      }
      throw new Error(`Failed to fetch dhae stats: ${error.message}`)
    }

    const stats = {
      total: data.length,
      active: data.filter(d => d.status === 'active').length,
      inactive: data.filter(d => d.status === 'inactive').length,
      onLeave: data.filter(d => d.status === 'on_leave').length,
      averageRating: data.length > 0
        ? Number((data.reduce((sum, d) => sum + (d.rating || 0), 0) / data.length).toFixed(1))
        : 0,
      totalAssignments: data.reduce((sum, d) => sum + (d.total_assignments || 0), 0),
      averageExperience: data.length > 0
        ? Math.round(data.reduce((sum, d) => sum + (d.experience_years || 0), 0) / data.length)
        : 0,
    }

    return stats
  }

  static async getAvailableForDate(organizationId: string, date: string, excludeScheduleId?: string) {
    // Get dhae who are not already scheduled for this date
    let query = supabase
      .from('dhae')
      .select(`
        *,
        jummah_schedules!left (
          id,
          schedule_date,
          status
        )
      `)
      .eq('organization_id', organizationId)
      .eq('status', 'active')
      .eq('is_deleted', false)

    const { data, error } = await query

    if (error) {
      throw new Error(`Failed to fetch available dhae: ${error.message}`)
    }

    // Filter out dhae who already have a schedule on this date
    const available = data?.filter(dhae => {
      const schedules = dhae.jummah_schedules || []
      return !schedules.some((schedule: any) => 
        schedule.schedule_date === date && 
        schedule.status !== 'cancelled' &&
        schedule.id !== excludeScheduleId
      )
    })

    return available || []
  }

  static async updateRating(id: string, organizationId: string, newRating: number) {
    // Get current rating and total assignments
    const { data: currentDhae, error: fetchError } = await supabase
      .from('dhae')
      .select('rating, total_assignments')
      .eq('id', id)
      .eq('organization_id', organizationId)
      .single()

    if (fetchError) {
      throw new Error(`Failed to fetch current dhae data: ${fetchError.message}`)
    }

    // Calculate new average rating
    const currentRating = currentDhae.rating || 0
    const totalAssignments = currentDhae.total_assignments || 0
    const newAverageRating = totalAssignments > 0 
      ? ((currentRating * totalAssignments) + newRating) / (totalAssignments + 1)
      : newRating

    const { data, error } = await supabase
      .from('dhae')
      .update({
        rating: Number(newAverageRating.toFixed(2)),
        total_assignments: totalAssignments + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('organization_id', organizationId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update dhae rating: ${error.message}`)
    }

    return data
  }
}
