// Mock data service for when database is not set up

export const mockDhaeData = [
  {
    id: '1',
    organization_id: 'mock-org-id',
    dhae_name: 'Imam <PERSON> bin <PERSON>',
    dhae_contact: '+60123456789',
    dhae_email: '<EMAIL>',
    address_line1: '123 Jalan Imam',
    address_line2: null,
    city_id: '1',
    district: 'Ampang',
    qualifications: ['Ijazah in Quran', 'Islamic Studies Degree'],
    languages: ['Arabic', 'Malay', 'English'],
    specializations: ['Tafsir', 'Hadith'],
    experience_years: 15,
    availability_pattern: null,
    travel_radius: 50,
    rating: 4.8,
    total_assignments: 120,
    notes: null,
    emergency_contact: null,
    documents: null,
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_deleted: false,
    cities: {
      id: '1',
      name: 'Kuala Lumpur',
      state: 'Federal Territory'
    }
  },
  {
    id: '2',
    organization_id: 'mock-org-id',
    dhae_name: '<PERSON>',
    dhae_contact: '+60123456790',
    dhae_email: '<EMAIL>',
    address_line1: '456 Jalan Sheikh',
    address_line2: null,
    city_id: '2',
    district: 'Petaling Jaya',
    qualifications: ['PhD Islamic Studies', 'Hafiz'],
    languages: ['Arabic', 'Malay', 'English', 'Urdu'],
    specializations: ['Fiqh', 'Aqidah', 'Tafsir'],
    experience_years: 20,
    availability_pattern: null,
    travel_radius: 30,
    rating: 4.9,
    total_assignments: 200,
    notes: null,
    emergency_contact: null,
    documents: null,
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_deleted: false,
    cities: {
      id: '2',
      name: 'Petaling Jaya',
      state: 'Selangor'
    }
  },
  {
    id: '3',
    organization_id: 'mock-org-id',
    dhae_name: 'Ustaz Ahmad bin Ali',
    dhae_contact: '+60123456791',
    dhae_email: '<EMAIL>',
    address_line1: '789 Jalan Ustaz',
    address_line2: null,
    city_id: '1',
    district: 'Cheras',
    qualifications: ['Islamic Studies Diploma'],
    languages: ['Malay', 'English'],
    specializations: ['Youth Programs', 'Community Outreach'],
    experience_years: 8,
    availability_pattern: null,
    travel_radius: 25,
    rating: 4.6,
    total_assignments: 85,
    notes: null,
    emergency_contact: null,
    documents: null,
    status: 'active' as const,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_deleted: false,
    cities: {
      id: '1',
      name: 'Kuala Lumpur',
      state: 'Federal Territory'
    }
  }
]

export const mockScheduleData = [
  {
    id: '1',
    organization_id: 'mock-org-id',
    dhae_id: '1',
    branch_id: '1',
    schedule_date: '2024-01-12',
    recurrence_pattern: null,
    recurrence_end_date: null,
    parent_schedule_id: null,
    status: 'confirmed' as const,
    priority: 'normal' as const,
    estimated_attendance: 450,
    actual_attendance: null,
    feedback_rating: null,
    feedback_notes: null,
    special_requirements: null,
    reminders_sent: {},
    travel_allowance: null,
    notes: null,
    created_by: 'mock-user',
    approved_by: null,
    approved_at: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    branches: {
      id: '1',
      branch_name: 'Masjid Al-Noor',
      branch_code: 'MAN001',
      district: 'Wangsa Maju'
    },
    dhae: {
      id: '1',
      dhae_name: 'Imam Abdullah bin Mohammed',
      dhae_contact: '+60123456789',
      rating: 4.8
    }
  },
  {
    id: '2',
    organization_id: 'mock-org-id',
    dhae_id: '2',
    branch_id: '2',
    schedule_date: '2024-01-12',
    recurrence_pattern: null,
    recurrence_end_date: null,
    parent_schedule_id: null,
    status: 'pending' as const,
    priority: 'high' as const,
    estimated_attendance: 600,
    actual_attendance: null,
    feedback_rating: null,
    feedback_notes: null,
    special_requirements: null,
    reminders_sent: {},
    travel_allowance: null,
    notes: null,
    created_by: 'mock-user',
    approved_by: null,
    approved_at: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    branches: {
      id: '2',
      branch_name: 'Islamic Center KL',
      branch_code: 'IC002',
      district: 'KLCC'
    },
    dhae: {
      id: '2',
      dhae_name: 'Sheikh Mohammed bin Hassan',
      dhae_contact: '+60123456790',
      rating: 4.9
    }
  },
  {
    id: '3',
    organization_id: 'mock-org-id',
    dhae_id: '3',
    branch_id: '3',
    schedule_date: '2024-01-12',
    recurrence_pattern: null,
    recurrence_end_date: null,
    parent_schedule_id: null,
    status: 'confirmed' as const,
    priority: 'normal' as const,
    estimated_attendance: 250,
    actual_attendance: null,
    feedback_rating: null,
    feedback_notes: null,
    special_requirements: null,
    reminders_sent: {},
    travel_allowance: null,
    notes: null,
    created_by: 'mock-user',
    approved_by: null,
    approved_at: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    branches: {
      id: '3',
      branch_name: 'Community Mosque',
      branch_code: 'CM003',
      district: 'Shah Alam'
    },
    dhae: {
      id: '3',
      dhae_name: 'Ustaz Ahmad bin Ali',
      dhae_contact: '+60123456791',
      rating: 4.6
    }
  },
  {
    id: '4',
    organization_id: 'mock-org-id',
    dhae_id: null,
    branch_id: '1',
    schedule_date: '2024-01-19',
    recurrence_pattern: null,
    recurrence_end_date: null,
    parent_schedule_id: null,
    status: 'scheduled' as const,
    priority: 'urgent' as const,
    estimated_attendance: 300,
    actual_attendance: null,
    feedback_rating: null,
    feedback_notes: null,
    special_requirements: null,
    reminders_sent: {},
    travel_allowance: null,
    notes: null,
    created_by: 'mock-user',
    approved_by: null,
    approved_at: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    branches: {
      id: '1',
      branch_name: 'Masjid Al-Noor',
      branch_code: 'MAN001',
      district: 'Wangsa Maju'
    },
    dhae: null
  }
]

export function getMockDhaeStats() {
  return {
    total: mockDhaeData.length,
    active: mockDhaeData.filter(d => d.status === 'active').length,
    inactive: mockDhaeData.filter(d => d.status === 'inactive').length,
    onLeave: mockDhaeData.filter(d => d.status === 'on_leave').length,
    averageRating: Number((mockDhaeData.reduce((sum, d) => sum + d.rating, 0) / mockDhaeData.length).toFixed(1)),
    totalAssignments: mockDhaeData.reduce((sum, d) => sum + d.total_assignments, 0),
    averageExperience: Math.round(mockDhaeData.reduce((sum, d) => sum + d.experience_years, 0) / mockDhaeData.length),
  }
}

export function getMockScheduleStats() {
  const today = new Date().toISOString().split('T')[0]
  const weekFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  
  const upcoming = mockScheduleData.filter(s => s.schedule_date >= today)
  const thisWeek = mockScheduleData.filter(s => s.schedule_date >= today && s.schedule_date <= weekFromNow)

  return {
    total: mockScheduleData.length,
    upcoming: upcoming.length,
    thisWeek: thisWeek.length,
    confirmed: upcoming.filter(s => s.status === 'confirmed').length,
    pending: upcoming.filter(s => s.status === 'pending').length,
    unassigned: upcoming.filter(s => !s.dhae_id).length,
    totalExpectedAttendance: thisWeek.reduce((sum, s) => sum + (s.estimated_attendance || 0), 0),
    completionRate: mockScheduleData.length > 0 
      ? Math.round((mockScheduleData.filter(s => s.status === 'completed').length / mockScheduleData.length) * 100)
      : 0,
  }
}
