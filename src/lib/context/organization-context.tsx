'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { supabase } from '@/lib/supabase'

interface Organization {
  id: string
  name: string
  slug: string
  logo_url?: string
  settings: Record<string, any>
  subscription_tier: string
}

interface UserProfile {
  id: string
  user_id: string
  full_name: string
  avatar_url?: string
  phone?: string
  role: 'admin' | 'manager' | 'coordinator' | 'user'
  organization_id: string
  preferences: Record<string, any>
}

interface OrganizationContextType {
  organization: Organization | null
  userProfile: UserProfile | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined)

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const { user, isLoaded } = useUser()
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchUserData = async () => {
    if (!user?.id) return

    try {
      setLoading(true)
      setError(null)

      // Check if database is set up by testing a simple query
      const { error: testError } = await supabase
        .from('organizations')
        .select('id')
        .limit(1)

      if (testError) {
        // Database not set up yet, use mock data
        console.warn('Database not set up, using mock data:', testError.message)

        const mockProfile: UserProfile = {
          id: 'mock-profile-id',
          user_id: user.id,
          full_name: user.fullName || user.firstName || 'User',
          avatar_url: user.imageUrl,
          role: 'admin',
          organization_id: 'mock-org-id',
          preferences: {}
        }

        const mockOrganization: Organization = {
          id: 'mock-org-id',
          name: 'Demo Organization',
          slug: 'demo-org',
          settings: { timezone: 'Asia/Kuala_Lumpur', currency: 'MYR' },
          subscription_tier: 'basic'
        }

        setUserProfile(mockProfile)
        setOrganization(mockOrganization)
        return
      }

      // First, try to get or create user profile
      let { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (profileError && profileError.code === 'PGRST116') {
        // User profile doesn't exist, create one
        const defaultOrgId = '550e8400-e29b-41d4-a716-446655440000' // Default organization from seed

        const { data: newProfile, error: createError } = await supabase
          .from('user_profiles')
          .insert({
            user_id: user.id,
            full_name: user.fullName || user.firstName || 'User',
            avatar_url: user.imageUrl,
            organization_id: defaultOrgId,
            role: 'user'
          })
          .select()
          .single()

        if (createError) {
          throw createError
        }

        profile = newProfile
      } else if (profileError) {
        throw profileError
      }

      setUserProfile(profile)

      // Get organization data
      if (profile?.organization_id) {
        const { data: org, error: orgError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', profile.organization_id)
          .single()

        if (orgError) {
          throw orgError
        }

        setOrganization(org)
      }
    } catch (err) {
      console.error('Error fetching user data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isLoaded && user) {
      fetchUserData()
    } else if (isLoaded && !user) {
      setLoading(false)
      setOrganization(null)
      setUserProfile(null)
    }
  }, [user, isLoaded])

  const refetch = async () => {
    await fetchUserData()
  }

  return (
    <OrganizationContext.Provider
      value={{
        organization,
        userProfile,
        loading,
        error,
        refetch,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  )
}

export function useOrganization() {
  const context = useContext(OrganizationContext)
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider')
  }
  return context
}
