export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          slug: string
          logo_url: string | null
          settings: Record<string, any>
          subscription_tier: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          logo_url?: string | null
          settings?: Record<string, any>
          subscription_tier?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          logo_url?: string | null
          settings?: Record<string, any>
          subscription_tier?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          user_id: string
          full_name: string
          avatar_url: string | null
          phone: string | null
          role: 'admin' | 'manager' | 'coordinator' | 'user'
          organization_id: string | null
          preferences: Record<string, any>
          last_active: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          full_name: string
          avatar_url?: string | null
          phone?: string | null
          role?: 'admin' | 'manager' | 'coordinator' | 'user'
          organization_id?: string | null
          preferences?: Record<string, any>
          last_active?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          full_name?: string
          avatar_url?: string | null
          phone?: string | null
          role?: 'admin' | 'manager' | 'coordinator' | 'user'
          organization_id?: string | null
          preferences?: Record<string, any>
          last_active?: string
          created_at?: string
          updated_at?: string
        }
      }
      branches: {
        Row: {
          id: string
          organization_id: string
          branch_code: string
          branch_name: string
          jip_name: string | null
          jip_contact: string | null
          jip_email: string | null
          address_line1: string | null
          address_line2: string | null
          city_id: string | null
          district: string | null
          postal_code: string | null
          latitude: number | null
          longitude: number | null
          capacity: number | null
          facilities: string[] | null
          prayer_times: Record<string, any> | null
          contact_person: Record<string, any> | null
          status: 'active' | 'inactive' | 'maintenance'
          created_at: string
          updated_at: string
          is_deleted: boolean
        }
        Insert: {
          id?: string
          organization_id: string
          branch_code: string
          branch_name: string
          jip_name?: string | null
          jip_contact?: string | null
          jip_email?: string | null
          address_line1?: string | null
          address_line2?: string | null
          city_id?: string | null
          district?: string | null
          postal_code?: string | null
          latitude?: number | null
          longitude?: number | null
          capacity?: number | null
          facilities?: string[] | null
          prayer_times?: Record<string, any> | null
          contact_person?: Record<string, any> | null
          status?: 'active' | 'inactive' | 'maintenance'
          created_at?: string
          updated_at?: string
          is_deleted?: boolean
        }
        Update: {
          id?: string
          organization_id?: string
          branch_code?: string
          branch_name?: string
          jip_name?: string | null
          jip_contact?: string | null
          jip_email?: string | null
          address_line1?: string | null
          address_line2?: string | null
          city_id?: string | null
          district?: string | null
          postal_code?: string | null
          latitude?: number | null
          longitude?: number | null
          capacity?: number | null
          facilities?: string[] | null
          prayer_times?: Record<string, any> | null
          contact_person?: Record<string, any> | null
          status?: 'active' | 'inactive' | 'maintenance'
          created_at?: string
          updated_at?: string
          is_deleted?: boolean
        }
      }
      dhae: {
        Row: {
          id: string
          organization_id: string
          dhae_name: string
          dhae_contact: string | null
          dhae_email: string | null
          address_line1: string | null
          address_line2: string | null
          city_id: string | null
          district: string | null
          qualifications: string[] | null
          languages: string[] | null
          specializations: string[] | null
          experience_years: number | null
          availability_pattern: Record<string, any> | null
          travel_radius: number | null
          rating: number
          total_assignments: number
          notes: string | null
          emergency_contact: Record<string, any> | null
          documents: Record<string, any>[] | null
          status: 'active' | 'inactive' | 'on_leave'
          created_at: string
          updated_at: string
          is_deleted: boolean
        }
        Insert: {
          id?: string
          organization_id: string
          dhae_name: string
          dhae_contact?: string | null
          dhae_email?: string | null
          address_line1?: string | null
          address_line2?: string | null
          city_id?: string | null
          district?: string | null
          qualifications?: string[] | null
          languages?: string[] | null
          specializations?: string[] | null
          experience_years?: number | null
          availability_pattern?: Record<string, any> | null
          travel_radius?: number | null
          rating?: number
          total_assignments?: number
          notes?: string | null
          emergency_contact?: Record<string, any> | null
          documents?: Record<string, any>[] | null
          status?: 'active' | 'inactive' | 'on_leave'
          created_at?: string
          updated_at?: string
          is_deleted?: boolean
        }
        Update: {
          id?: string
          organization_id?: string
          dhae_name?: string
          dhae_contact?: string | null
          dhae_email?: string | null
          address_line1?: string | null
          address_line2?: string | null
          city_id?: string | null
          district?: string | null
          qualifications?: string[] | null
          languages?: string[] | null
          specializations?: string[] | null
          experience_years?: number | null
          availability_pattern?: Record<string, any> | null
          travel_radius?: number | null
          rating?: number
          total_assignments?: number
          notes?: string | null
          emergency_contact?: Record<string, any> | null
          documents?: Record<string, any>[] | null
          status?: 'active' | 'inactive' | 'on_leave'
          created_at?: string
          updated_at?: string
          is_deleted?: boolean
        }
      }
      jummah_schedules: {
        Row: {
          id: string
          organization_id: string
          dhae_id: string | null
          branch_id: string
          schedule_date: string
          recurrence_pattern: Record<string, any> | null
          recurrence_end_date: string | null
          parent_schedule_id: string | null
          status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'rescheduled'
          priority: 'low' | 'normal' | 'high' | 'urgent'
          estimated_attendance: number | null
          actual_attendance: number | null
          feedback_rating: number | null
          feedback_notes: string | null
          special_requirements: string[] | null
          reminders_sent: Record<string, any>
          travel_allowance: number | null
          notes: string | null
          created_by: string | null
          approved_by: string | null
          approved_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          dhae_id?: string | null
          branch_id: string
          schedule_date: string
          recurrence_pattern?: Record<string, any> | null
          recurrence_end_date?: string | null
          parent_schedule_id?: string | null
          status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'rescheduled'
          priority?: 'low' | 'normal' | 'high' | 'urgent'
          estimated_attendance?: number | null
          actual_attendance?: number | null
          feedback_rating?: number | null
          feedback_notes?: string | null
          special_requirements?: string[] | null
          reminders_sent?: Record<string, any>
          travel_allowance?: number | null
          notes?: string | null
          created_by?: string | null
          approved_by?: string | null
          approved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          dhae_id?: string | null
          branch_id?: string
          schedule_date?: string
          recurrence_pattern?: Record<string, any> | null
          recurrence_end_date?: string | null
          parent_schedule_id?: string | null
          status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'rescheduled'
          priority?: 'low' | 'normal' | 'high' | 'urgent'
          estimated_attendance?: number | null
          actual_attendance?: number | null
          feedback_rating?: number | null
          feedback_notes?: string | null
          special_requirements?: string[] | null
          reminders_sent?: Record<string, any>
          travel_allowance?: number | null
          notes?: string | null
          created_by?: string | null
          approved_by?: string | null
          approved_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
