'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ScheduleService } from '@/lib/services/schedule-service'
import { ScheduleFormData, ScheduleFilter } from '@/lib/validations/schemas'
import { toast } from 'sonner'

const QUERY_KEYS = {
  schedules: (orgId: string, filter?: ScheduleFilter) => ['schedules', orgId, filter],
  schedule: (id: string, orgId: string) => ['schedule', id, orgId],
  scheduleStats: (orgId: string) => ['schedule-stats', orgId],
  upcomingSchedules: (orgId: string, limit: number) => ['upcoming-schedules', orgId, limit],
  conflicts: (orgId: string, dhaeId: string, date: string) => ['schedule-conflicts', orgId, dhaeId, date],
}

export function useSchedules(organizationId: string, filter?: ScheduleFilter) {
  return useQuery({
    queryKey: QUERY_KEYS.schedules(organizationId, filter),
    queryFn: () => ScheduleService.getAll(organizationId, filter),
    enabled: !!organizationId,
  })
}

export function useSchedule(id: string, organizationId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.schedule(id, organizationId),
    queryFn: () => ScheduleService.getById(id, organizationId),
    enabled: !!id && !!organizationId,
  })
}

export function useScheduleStats(organizationId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.scheduleStats(organizationId),
    queryFn: () => ScheduleService.getStats(organizationId),
    enabled: !!organizationId,
  })
}

export function useUpcomingSchedules(organizationId: string, limit: number = 10) {
  return useQuery({
    queryKey: QUERY_KEYS.upcomingSchedules(organizationId, limit),
    queryFn: () => ScheduleService.getUpcoming(organizationId, limit),
    enabled: !!organizationId,
  })
}

export function useScheduleConflicts(organizationId: string, dhaeId: string, date: string) {
  return useQuery({
    queryKey: QUERY_KEYS.conflicts(organizationId, dhaeId, date),
    queryFn: () => ScheduleService.checkConflicts(organizationId, dhaeId, date),
    enabled: !!organizationId && !!dhaeId && !!date,
  })
}

export function useCreateSchedule(organizationId: string, createdBy: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ScheduleFormData) => ScheduleService.create(organizationId, data, createdBy),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['schedule-stats', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['upcoming-schedules', organizationId] })
      toast.success('Schedule created successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to create schedule: ${error.message}`)
    },
  })
}

export function useUpdateSchedule(id: string, organizationId: string, updatedBy: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Partial<ScheduleFormData>) => 
      ScheduleService.update(id, organizationId, data, updatedBy),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['schedule', id, organizationId] })
      queryClient.invalidateQueries({ queryKey: ['schedule-stats', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['upcoming-schedules', organizationId] })
      toast.success('Schedule updated successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to update schedule: ${error.message}`)
    },
  })
}

export function useDeleteSchedule(organizationId: string, deletedBy: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) => 
      ScheduleService.delete(id, organizationId, deletedBy, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['schedule-stats', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['upcoming-schedules', organizationId] })
      toast.success('Schedule deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete schedule: ${error.message}`)
    },
  })
}

export function useApproveSchedule(organizationId: string, approvedBy: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => ScheduleService.approve(id, organizationId, approvedBy),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['schedule-stats', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['upcoming-schedules', organizationId] })
      toast.success('Schedule approved successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to approve schedule: ${error.message}`)
    },
  })
}
