'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { BranchService } from '@/lib/services/branch-service'
import { BranchFormData, BranchFilter } from '@/lib/validations/schemas'
import { toast } from 'sonner'

const QUERY_KEYS = {
  branches: (orgId: string, filter?: BranchFilter) => ['branches', orgId, filter],
  branch: (id: string, orgId: string) => ['branch', id, orgId],
  branchStats: (orgId: string) => ['branch-stats', orgId],
}

export function useBranches(organizationId: string, filter?: BranchFilter) {
  return useQuery({
    queryKey: QUERY_KEYS.branches(organizationId, filter),
    queryFn: () => BranchService.getAll(organizationId, filter),
    enabled: !!organizationId,
  })
}

export function useBranch(id: string, organizationId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.branch(id, organizationId),
    queryFn: () => BranchService.getById(id, organizationId),
    enabled: !!id && !!organizationId,
  })
}

export function useBranchStats(organizationId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.branchStats(organizationId),
    queryFn: () => BranchService.getStats(organizationId),
    enabled: !!organizationId,
  })
}

export function useCreateBranch(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: BranchFormData) => BranchService.create(organizationId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['branches', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['branch-stats', organizationId] })
      toast.success('Branch created successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to create branch: ${error.message}`)
    },
  })
}

export function useUpdateBranch(id: string, organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Partial<BranchFormData>) => BranchService.update(id, organizationId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['branches', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['branch', id, organizationId] })
      queryClient.invalidateQueries({ queryKey: ['branch-stats', organizationId] })
      toast.success('Branch updated successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to update branch: ${error.message}`)
    },
  })
}

export function useDeleteBranch(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => BranchService.delete(id, organizationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['branches', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['branch-stats', organizationId] })
      toast.success('Branch deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete branch: ${error.message}`)
    },
  })
}
