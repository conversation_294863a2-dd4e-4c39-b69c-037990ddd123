'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { DhaeService } from '@/lib/services/dhae-service'
import { DhaeFormData, DhaeFilter } from '@/lib/validations/schemas'
import { toast } from 'sonner'

const QUERY_KEYS = {
  dhae: (orgId: string, filter?: DhaeFilter) => ['dhae', orgId, filter],
  dhaeById: (id: string, orgId: string) => ['dhae', id, orgId],
  dhaeStats: (orgId: string) => ['dhae-stats', orgId],
  availableDhae: (orgId: string, date: string) => ['available-dhae', orgId, date],
}

export function useDhae(organizationId: string, filter?: DhaeFilter) {
  return useQuery({
    queryKey: QUERY_KEYS.dhae(organizationId, filter),
    queryFn: () => DhaeService.getAll(organizationId, filter),
    enabled: !!organizationId,
  })
}

export function useDhaeById(id: string, organizationId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.dhaeById(id, organizationId),
    queryFn: () => DhaeService.getById(id, organizationId),
    enabled: !!id && !!organizationId,
  })
}

export function useDhaeStats(organizationId: string) {
  return useQuery({
    queryKey: QUERY_KEYS.dhaeStats(organizationId),
    queryFn: () => DhaeService.getStats(organizationId),
    enabled: !!organizationId,
  })
}

export function useAvailableDhae(organizationId: string, date: string) {
  return useQuery({
    queryKey: QUERY_KEYS.availableDhae(organizationId, date),
    queryFn: () => DhaeService.getAvailableForDate(organizationId, date),
    enabled: !!organizationId && !!date,
  })
}

export function useCreateDhae(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: DhaeFormData) => DhaeService.create(organizationId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dhae', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['dhae-stats', organizationId] })
      toast.success('Dhae created successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to create dhae: ${error.message}`)
    },
  })
}

export function useUpdateDhae(id: string, organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: Partial<DhaeFormData>) => DhaeService.update(id, organizationId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dhae', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['dhae', id, organizationId] })
      queryClient.invalidateQueries({ queryKey: ['dhae-stats', organizationId] })
      toast.success('Dhae updated successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to update dhae: ${error.message}`)
    },
  })
}

export function useDeleteDhae(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => DhaeService.delete(id, organizationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dhae', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['dhae-stats', organizationId] })
      toast.success('Dhae deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete dhae: ${error.message}`)
    },
  })
}

export function useUpdateDhaeRating(organizationId: string) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, rating }: { id: string; rating: number }) => 
      DhaeService.updateRating(id, organizationId, rating),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dhae', organizationId] })
      queryClient.invalidateQueries({ queryKey: ['dhae-stats', organizationId] })
      toast.success('Rating updated successfully')
    },
    onError: (error: Error) => {
      toast.error(`Failed to update rating: ${error.message}`)
    },
  })
}
