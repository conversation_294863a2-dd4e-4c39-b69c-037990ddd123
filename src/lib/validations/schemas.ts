import { z } from 'zod'

// Branch validation schema
export const branchSchema = z.object({
  branch_code: z.string().min(1, 'Branch code is required').max(20, 'Branch code too long'),
  branch_name: z.string().min(1, 'Branch name is required').max(100, 'Branch name too long'),
  jip_name: z.string().optional(),
  jip_contact: z.string().optional(),
  jip_email: z.string().email('Invalid email format').optional().or(z.literal('')),
  address_line1: z.string().optional(),
  address_line2: z.string().optional(),
  city_id: z.string().uuid('Invalid city ID').optional(),
  district: z.string().optional(),
  postal_code: z.string().optional(),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  capacity: z.number().int().min(1, 'Capacity must be at least 1').optional(),
  facilities: z.array(z.string()).optional(),
  prayer_times: z.record(z.string()).optional(),
  contact_person: z.record(z.any()).optional(),
  status: z.enum(['active', 'inactive', 'maintenance']).default('active'),
})

export type BranchFormData = z.infer<typeof branchSchema>

// Dhae validation schema
export const dhaeSchema = z.object({
  dhae_name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  dhae_contact: z.string().optional(),
  dhae_email: z.string().email('Invalid email format').optional().or(z.literal('')),
  address_line1: z.string().optional(),
  address_line2: z.string().optional(),
  city_id: z.string().uuid('Invalid city ID').optional(),
  district: z.string().optional(),
  qualifications: z.array(z.string()).optional(),
  languages: z.array(z.string()).optional(),
  specializations: z.array(z.string()).optional(),
  experience_years: z.number().int().min(0, 'Experience cannot be negative').optional(),
  availability_pattern: z.record(z.any()).optional(),
  travel_radius: z.number().int().min(0, 'Travel radius cannot be negative').optional(),
  notes: z.string().optional(),
  emergency_contact: z.record(z.any()).optional(),
  status: z.enum(['active', 'inactive', 'on_leave']).default('active'),
})

export type DhaeFormData = z.infer<typeof dhaeSchema>

// Schedule validation schema
export const scheduleSchema = z.object({
  dhae_id: z.string().uuid('Invalid dhae ID').optional(),
  branch_id: z.string().uuid('Invalid branch ID'),
  schedule_date: z.string().refine((date) => {
    const parsedDate = new Date(date)
    return !isNaN(parsedDate.getTime()) && parsedDate >= new Date()
  }, 'Schedule date must be today or in the future'),
  recurrence_pattern: z.record(z.any()).optional(),
  recurrence_end_date: z.string().optional(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled']).default('scheduled'),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  estimated_attendance: z.number().int().min(1, 'Estimated attendance must be at least 1').optional(),
  special_requirements: z.array(z.string()).optional(),
  travel_allowance: z.number().min(0, 'Travel allowance cannot be negative').optional(),
  notes: z.string().optional(),
})

export type ScheduleFormData = z.infer<typeof scheduleSchema>

// User profile validation schema
export const userProfileSchema = z.object({
  full_name: z.string().min(1, 'Full name is required').max(100, 'Name too long'),
  phone: z.string().optional(),
  role: z.enum(['admin', 'manager', 'coordinator', 'user']).default('user'),
  preferences: z.record(z.any()).optional(),
})

export type UserProfileFormData = z.infer<typeof userProfileSchema>

// Organization validation schema
export const organizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required').max(100, 'Name too long'),
  slug: z.string().min(1, 'Slug is required').max(50, 'Slug too long')
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  logo_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  settings: z.record(z.any()).optional(),
  subscription_tier: z.enum(['basic', 'premium', 'enterprise']).default('basic'),
})

export type OrganizationFormData = z.infer<typeof organizationSchema>

// Search and filter schemas
export const branchFilterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(['active', 'inactive', 'maintenance']).optional(),
  city_id: z.string().uuid().optional(),
  capacity_min: z.number().int().min(0).optional(),
  capacity_max: z.number().int().min(0).optional(),
})

export const dhaeFilterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(['active', 'inactive', 'on_leave']).optional(),
  city_id: z.string().uuid().optional(),
  specializations: z.array(z.string()).optional(),
  languages: z.array(z.string()).optional(),
  experience_min: z.number().int().min(0).optional(),
  rating_min: z.number().min(0).max(5).optional(),
})

export const scheduleFilterSchema = z.object({
  search: z.string().optional(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled']).optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
  branch_id: z.string().uuid().optional(),
  dhae_id: z.string().uuid().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
})

export type BranchFilter = z.infer<typeof branchFilterSchema>
export type DhaeFilter = z.infer<typeof dhaeFilterSchema>
export type ScheduleFilter = z.infer<typeof scheduleFilterSchema>
