'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  Calendar, 
  Building2, 
  Users, 
  BarChart3, 
  Settings,
  Home,
  CalendarDays,
  UserCheck
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
  },
  {
    name: 'Branches',
    href: '/dashboard/branches',
    icon: Building2,
  },
  {
    name: 'Dhae',
    href: '/dashboard/dhae',
    icon: Users,
  },
  {
    name: 'Scheduling',
    href: '/dashboard/scheduling',
    icon: Calendar,
    children: [
      {
        name: 'Calendar View',
        href: '/dashboard/scheduling/calendar',
        icon: CalendarDays,
      },
      {
        name: 'Assignments',
        href: '/dashboard/scheduling/assignments',
        icon: UserCheck,
      },
    ],
  },
  {
    name: 'Reports',
    href: '/dashboard/reports',
    icon: BarChart3,
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-card border-r border-border">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-foreground">
          Jummah Management
        </h2>
      </div>
      
      <nav className="px-4 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
          
          return (
            <div key={item.name}>
              <Link href={item.href}>
                <Button
                  variant={isActive ? 'secondary' : 'ghost'}
                  className={cn(
                    'w-full justify-start',
                    isActive && 'bg-secondary text-secondary-foreground'
                  )}
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.name}
                </Button>
              </Link>
              
              {item.children && isActive && (
                <div className="ml-6 mt-2 space-y-1">
                  {item.children.map((child) => {
                    const isChildActive = pathname === child.href
                    
                    return (
                      <Link key={child.name} href={child.href}>
                        <Button
                          variant={isChildActive ? 'secondary' : 'ghost'}
                          size="sm"
                          className={cn(
                            'w-full justify-start',
                            isChildActive && 'bg-secondary text-secondary-foreground'
                          )}
                        >
                          <child.icon className="mr-2 h-3 w-3" />
                          {child.name}
                        </Button>
                      </Link>
                    )
                  })}
                </div>
              )}
            </div>
          )
        })}
      </nav>
    </div>
  )
}
