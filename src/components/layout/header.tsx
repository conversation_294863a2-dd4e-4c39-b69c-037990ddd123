'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs'
import { Bell } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'

export function Header() {
  return (
    <header className="h-16 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center justify-between px-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-semibold">Dashboard</h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon">
            <Bell className="h-4 w-4" />
          </Button>
          <UserButton />
        </div>
      </div>
    </header>
  )
}
