'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, MapPin, Phone, Mail } from 'lucide-react'
import { useOrganization } from '@/lib/context/organization-context'
import { useBranches, useBranchStats } from '@/lib/hooks/use-branches'
import { useState } from 'react'

export default function BranchesPage() {
  const { organization } = useOrganization()
  const [searchTerm, setSearchTerm] = useState('')

  const { data: branches, isLoading } = useBranches(organization?.id || '')
  const { data: stats } = useBranchStats(organization?.id || '')

  const filteredBranches = branches?.filter(branch =>
    branch.branch_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.branch_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.district?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Branches</h2>
          <p className="text-muted-foreground">
            Manage prayer locations and their details
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Branch
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Branches</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, code, or location..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button variant="outline">Filter</Button>
          </div>
        </CardContent>
      </Card>

      {/* Branches Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredBranches.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBranches.map((branch) => (
            <Card key={branch.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{branch.branch_name}</CardTitle>
                    <CardDescription>Code: {branch.branch_code}</CardDescription>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    branch.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : branch.status === 'inactive'
                      ? 'bg-gray-100 text-gray-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {branch.status}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      {branch.address_line1}
                      {branch.district && `, ${branch.district}`}
                      {branch.cities && `, ${branch.cities.name}`}
                    </span>
                  </div>
                  {branch.capacity && (
                    <div className="flex items-center text-sm">
                      <span className="mr-2 text-muted-foreground">Capacity:</span>
                      <span>{branch.capacity} people</span>
                    </div>
                  )}
                </div>

                {branch.jip_name && (
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2">JIP Contact</h4>
                    <div className="space-y-1 text-sm">
                      <div>{branch.jip_name}</div>
                      {branch.jip_contact && (
                        <div className="flex items-center">
                          <Phone className="mr-2 h-3 w-3 text-muted-foreground" />
                          <span>{branch.jip_contact}</span>
                        </div>
                      )}
                      {branch.jip_email && (
                        <div className="flex items-center">
                          <Mail className="mr-2 h-3 w-3 text-muted-foreground" />
                          <span>{branch.jip_email}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button variant="outline" size="sm" className="flex-1">
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">
              {searchTerm ? 'No branches found matching your search.' : 'No branches found.'}
            </p>
            <Button className="mt-4">
              <Plus className="mr-2 h-4 w-4" />
              Add First Branch
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">Total Branches</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats?.active || 0}</div>
            <p className="text-xs text-muted-foreground">Active Branches</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats?.totalCapacity || 0}</div>
            <p className="text-xs text-muted-foreground">Total Capacity</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats?.averageCapacity || 0}</div>
            <p className="text-xs text-muted-foreground">Avg Capacity</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
