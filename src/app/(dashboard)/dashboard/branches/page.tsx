import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, MapPin, Phone, Mail } from 'lucide-react'

// Mock data - in real app this would come from Supabase
const branches = [
  {
    id: '1',
    branch_code: 'MAN001',
    branch_name: 'Mas<PERSON> Al-Noor',
    jip_name: '<PERSON>',
    jip_contact: '+60123456789',
    jip_email: '<EMAIL>',
    address_line1: '123 Jalan Masjid',
    city: 'Kuala Lumpur',
    district: 'Wangsa Maju',
    capacity: 500,
    status: 'active' as const,
  },
  {
    id: '2',
    branch_code: 'IC002',
    branch_name: 'Islamic Center KL',
    jip_name: '<PERSON>',
    jip_contact: '+60123456790',
    jip_email: '<EMAIL>',
    address_line1: '456 Jalan Pusat Islam',
    city: 'Kuala Lumpur',
    district: 'KLCC',
    capacity: 800,
    status: 'active' as const,
  },
  {
    id: '3',
    branch_code: 'CM003',
    branch_name: 'Community Mosque',
    jip_name: '<PERSON> bin <PERSON>',
    jip_contact: '+60123456791',
    jip_email: '<EMAIL>',
    address_line1: '789 Jalan Komuniti',
    city: 'Selangor',
    district: 'Shah Alam',
    capacity: 300,
    status: 'active' as const,
  },
]

export default function BranchesPage() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Branches</h2>
          <p className="text-muted-foreground">
            Manage prayer locations and their details
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Branch
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Branches</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, code, or location..."
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter</Button>
          </div>
        </CardContent>
      </Card>

      {/* Branches Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {branches.map((branch) => (
          <Card key={branch.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{branch.branch_name}</CardTitle>
                  <CardDescription>Code: {branch.branch_code}</CardDescription>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  branch.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {branch.status}
                </span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{branch.address_line1}, {branch.district}, {branch.city}</span>
                </div>
                <div className="flex items-center text-sm">
                  <span className="mr-2 text-muted-foreground">Capacity:</span>
                  <span>{branch.capacity} people</span>
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">JIP Contact</h4>
                <div className="space-y-1 text-sm">
                  <div>{branch.jip_name}</div>
                  <div className="flex items-center">
                    <Phone className="mr-2 h-3 w-3 text-muted-foreground" />
                    <span>{branch.jip_contact}</span>
                  </div>
                  <div className="flex items-center">
                    <Mail className="mr-2 h-3 w-3 text-muted-foreground" />
                    <span>{branch.jip_email}</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button variant="outline" size="sm" className="flex-1">
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{branches.length}</div>
            <p className="text-xs text-muted-foreground">Total Branches</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {branches.filter(b => b.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">Active Branches</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {branches.reduce((sum, b) => sum + b.capacity, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Total Capacity</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {Math.round(branches.reduce((sum, b) => sum + b.capacity, 0) / branches.length)}
            </div>
            <p className="text-xs text-muted-foreground">Avg Capacity</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
