import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Calendar, Clock, MapPin, User } from 'lucide-react'

// Mock data - in real app this would come from Supabase
const upcomingSchedules = [
  {
    id: '1',
    branch_name: '<PERSON><PERSON><PERSON>',
    dhae_name: '<PERSON>',
    schedule_date: '2024-01-12',
    status: 'confirmed' as const,
    priority: 'normal' as const,
    estimated_attendance: 450,
  },
  {
    id: '2',
    branch_name: 'Islamic Center KL',
    dhae_name: '<PERSON>',
    schedule_date: '2024-01-12',
    status: 'pending' as const,
    priority: 'high' as const,
    estimated_attendance: 600,
  },
  {
    id: '3',
    branch_name: 'Community Mosque',
    dhae_name: '<PERSON><PERSON><PERSON>',
    schedule_date: '2024-01-12',
    status: 'confirmed' as const,
    priority: 'normal' as const,
    estimated_attendance: 250,
  },
  {
    id: '4',
    branch_name: '<PERSON><PERSON><PERSON>',
    dhae_name: null,
    schedule_date: '2024-01-19',
    status: 'scheduled' as const,
    priority: 'urgent' as const,
    estimated_attendance: 300,
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'scheduled':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'urgent':
      return 'bg-red-100 text-red-800'
    case 'high':
      return 'bg-orange-100 text-orange-800'
    case 'normal':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

export default function SchedulingPage() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Scheduling</h2>
          <p className="text-muted-foreground">
            Manage Jummah prayer schedules and assignments
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Schedule
        </Button>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div>
                <h3 className="font-semibold">Calendar View</h3>
                <p className="text-sm text-muted-foreground">View schedules in calendar format</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <User className="h-8 w-8 text-green-600" />
              <div>
                <h3 className="font-semibold">Assignments</h3>
                <p className="text-sm text-muted-foreground">Manage dhae assignments</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <Clock className="h-8 w-8 text-purple-600" />
              <div>
                <h3 className="font-semibold">Recurring</h3>
                <p className="text-sm text-muted-foreground">Set up recurring schedules</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Schedules */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Schedules</CardTitle>
          <CardDescription>
            Next week&apos;s Jummah prayer assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {upcomingSchedules.map((schedule) => (
              <div
                key={schedule.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex flex-col">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{schedule.branch_name}</span>
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {schedule.dhae_name || 'Not assigned'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="font-medium">{schedule.schedule_date}</div>
                    <div className="text-sm text-muted-foreground">
                      Est. {schedule.estimated_attendance} attendees
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(schedule.status)}`}>
                      {schedule.status}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs ${getPriorityColor(schedule.priority)}`}>
                      {schedule.priority}
                    </span>
                  </div>

                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{upcomingSchedules.length}</div>
            <p className="text-xs text-muted-foreground">Upcoming Schedules</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {upcomingSchedules.filter(s => s.status === 'confirmed').length}
            </div>
            <p className="text-xs text-muted-foreground">Confirmed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {upcomingSchedules.filter(s => !s.dhae_name).length}
            </div>
            <p className="text-xs text-muted-foreground">Unassigned</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {upcomingSchedules.reduce((sum, s) => sum + s.estimated_attendance, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Total Expected</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
