'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CalendarDays, Building2, Users, TrendingUp } from 'lucide-react'
import { useOrganization } from '@/lib/context/organization-context'
import { useBranchStats } from '@/lib/hooks/use-branches'
import { useDhaeStats } from '@/lib/hooks/use-dhae'
import { useScheduleStats } from '@/lib/hooks/use-schedules'
import { useUpcomingSchedules } from '@/lib/hooks/use-schedules'

export default function DashboardPage() {
  const { organization, userProfile, loading: orgLoading } = useOrganization()
  const { data: branchStats, isLoading: branchStatsLoading } = useBranchStats(organization?.id || '')
  const { data: dhaeStats, isLoading: dhaeStatsLoading } = useDhaeStats(organization?.id || '')
  const { data: scheduleStats, isLoading: scheduleStatsLoading } = useScheduleStats(organization?.id || '')
  const { data: upcomingSchedules, isLoading: upcomingLoading } = useUpcomingSchedules(organization?.id || '', 5)

  if (orgLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <p className="text-muted-foreground">
          Welcome back, {userProfile?.full_name}! Here&apos;s an overview of your Jummah management system.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {branchStatsLoading ? '...' : branchStats?.total || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {branchStatsLoading ? 'Loading...' : `${branchStats?.active || 0} active`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Dhae</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dhaeStatsLoading ? '...' : dhaeStats?.active || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {dhaeStatsLoading ? 'Loading...' : `${dhaeStats?.averageRating || 0} avg rating`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Week&apos;s Schedules</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scheduleStatsLoading ? '...' : scheduleStats?.thisWeek || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {scheduleStatsLoading ? 'Loading...' : `${scheduleStats?.confirmed || 0} confirmed`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scheduleStatsLoading ? '...' : `${scheduleStats?.completionRate || 0}%`}
            </div>
            <p className="text-xs text-muted-foreground">
              {scheduleStatsLoading ? 'Loading...' : `${scheduleStats?.unassigned || 0} unassigned`}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Schedules</CardTitle>
            <CardDescription>
              Latest Jummah schedule assignments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-sm text-muted-foreground">Loading schedules...</p>
                </div>
              ) : upcomingSchedules && upcomingSchedules.length > 0 ? (
                upcomingSchedules.map((schedule) => (
                  <div key={schedule.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{schedule.branches?.branch_name}</p>
                      <p className="text-sm text-muted-foreground">
                        {schedule.dhae?.dhae_name || 'Not assigned'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">{schedule.schedule_date}</p>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        schedule.status === 'confirmed'
                          ? 'bg-green-100 text-green-800'
                          : schedule.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {schedule.status}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No upcoming schedules found
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks to get you started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button className="w-full justify-start h-auto p-4">
                <CalendarDays className="mr-2 h-4 w-4" />
                <div className="text-left">
                  <div className="font-semibold">Schedule New Jummah</div>
                  <div className="text-sm text-muted-foreground">Create a new schedule assignment</div>
                </div>
              </Button>
              
              <Button variant="outline" className="w-full justify-start h-auto p-4">
                <Building2 className="mr-2 h-4 w-4" />
                <div className="text-left">
                  <div className="font-semibold">Add New Branch</div>
                  <div className="text-sm text-muted-foreground">Register a new prayer location</div>
                </div>
              </Button>
              
              <Button variant="outline" className="w-full justify-start h-auto p-4">
                <Users className="mr-2 h-4 w-4" />
                <div className="text-left">
                  <div className="font-semibold">Add New Dhae</div>
                  <div className="text-sm text-muted-foreground">Register a new imam or speaker</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
