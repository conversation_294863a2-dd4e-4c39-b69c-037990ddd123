import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CalendarDays, Building2, Users, TrendingUp } from 'lucide-react'

export default function DashboardPage() {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <p className="text-muted-foreground">
          Welcome back! Here's an overview of your Jummah management system.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              +2 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Dhae</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">
              +1 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Week's Schedules</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">
              All confirmed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">98%</div>
            <p className="text-xs text-muted-foreground">
              +2% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Schedules</CardTitle>
            <CardDescription>
              Latest Jummah schedule assignments
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { branch: 'Masjid Al-Noor', dhae: 'Imam Abdullah', date: '2024-01-12', status: 'Confirmed' },
                { branch: 'Islamic Center', dhae: 'Sheikh Mohammed', date: '2024-01-12', status: 'Pending' },
                { branch: 'Community Mosque', dhae: 'Ustaz Ahmad', date: '2024-01-12', status: 'Confirmed' },
              ].map((schedule, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{schedule.branch}</p>
                    <p className="text-sm text-muted-foreground">{schedule.dhae}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm">{schedule.date}</p>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      schedule.status === 'Confirmed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {schedule.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks to get you started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button className="w-full justify-start h-auto p-4">
                <CalendarDays className="mr-2 h-4 w-4" />
                <div className="text-left">
                  <div className="font-semibold">Schedule New Jummah</div>
                  <div className="text-sm text-muted-foreground">Create a new schedule assignment</div>
                </div>
              </Button>
              
              <Button variant="outline" className="w-full justify-start h-auto p-4">
                <Building2 className="mr-2 h-4 w-4" />
                <div className="text-left">
                  <div className="font-semibold">Add New Branch</div>
                  <div className="text-sm text-muted-foreground">Register a new prayer location</div>
                </div>
              </Button>
              
              <Button variant="outline" className="w-full justify-start h-auto p-4">
                <Users className="mr-2 h-4 w-4" />
                <div className="text-left">
                  <div className="font-semibold">Add New Dhae</div>
                  <div className="text-sm text-muted-foreground">Register a new imam or speaker</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
