import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Phone, Mail, Star, MapPin } from 'lucide-react'

// Mock data - in real app this would come from Supabase
const dhae = [
  {
    id: '1',
    dhae_name: 'Imam <PERSON>',
    dhae_contact: '+60123456789',
    dhae_email: '<EMAIL>',
    address_line1: '123 Jalan Imam',
    city: 'Kuala Lumpur',
    district: 'Ampang',
    qualifications: ['Ijazah in Quran', 'Islamic Studies Degree'],
    languages: ['Arabic', 'Malay', 'English'],
    specializations: ['Tafsir', 'Hadith'],
    experience_years: 15,
    rating: 4.8,
    total_assignments: 120,
    status: 'active' as const,
  },
  {
    id: '2',
    dhae_name: 'Sheikh <PERSON> bin <PERSON>',
    dhae_contact: '+60123456790',
    dhae_email: '<EMAIL>',
    address_line1: '456 Jalan Sheikh',
    city: 'Selangor',
    district: 'Petaling Jaya',
    qualifications: ['PhD Islamic Studies', 'Hafiz'],
    languages: ['Arabic', 'Malay', 'English', 'Urdu'],
    specializations: ['Fiqh', 'Aqidah', 'Tafsir'],
    experience_years: 20,
    rating: 4.9,
    total_assignments: 200,
    status: 'active' as const,
  },
  {
    id: '3',
    dhae_name: 'Ustaz Ahmad bin <PERSON>',
    dhae_contact: '+60123456791',
    dhae_email: '<EMAIL>',
    address_line1: '789 Jalan Ustaz',
    city: 'Kuala Lumpur',
    district: 'Cheras',
    qualifications: ['Islamic Studies Diploma'],
    languages: ['Malay', 'English'],
    specializations: ['Youth Programs', 'Community Outreach'],
    experience_years: 8,
    rating: 4.6,
    total_assignments: 85,
    status: 'active' as const,
  },
]

export default function DhaePage() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Dhae Management</h2>
          <p className="text-muted-foreground">
            Manage imams, speakers, and religious leaders
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Dhae
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search Dhae</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, specialization, or location..."
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter</Button>
          </div>
        </CardContent>
      </Card>

      {/* Dhae Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {dhae.map((person) => (
          <Card key={person.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{person.dhae_name}</CardTitle>
                  <CardDescription>{person.experience_years} years experience</CardDescription>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  person.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {person.status}
                </span>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Rating and Stats */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="ml-1 text-sm font-medium">{person.rating}</span>
                </div>
                <div className="text-sm text-muted-foreground">
                  {person.total_assignments} assignments
                </div>
              </div>

              {/* Contact Info */}
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{person.district}, {person.city}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{person.dhae_contact}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{person.dhae_email}</span>
                </div>
              </div>

              {/* Specializations */}
              <div>
                <h4 className="font-medium mb-2">Specializations</h4>
                <div className="flex flex-wrap gap-1">
                  {person.specializations.map((spec, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {spec}
                    </span>
                  ))}
                </div>
              </div>

              {/* Languages */}
              <div>
                <h4 className="font-medium mb-2">Languages</h4>
                <div className="flex flex-wrap gap-1">
                  {person.languages.map((lang, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"
                    >
                      {lang}
                    </span>
                  ))}
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button variant="outline" size="sm" className="flex-1">
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  View Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{dhae.length}</div>
            <p className="text-xs text-muted-foreground">Total Dhae</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {dhae.filter(d => d.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">Active Dhae</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {(dhae.reduce((sum, d) => sum + d.rating, 0) / dhae.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">Avg Rating</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {dhae.reduce((sum, d) => sum + d.total_assignments, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Total Assignments</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
