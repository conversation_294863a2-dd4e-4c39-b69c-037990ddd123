import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Clerk<PERSON>rovider } from '@clerk/nextjs'
import { QueryProvider } from '@/components/providers/query-provider'
import { OrganizationProvider } from '@/lib/context/organization-context'
import { Toaster } from 'sonner'
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Jummah Management System",
  description: "A comprehensive system to manage Jummah scheduling, branches, and dhae assignments",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <QueryProvider>
            <OrganizationProvider>
              {children}
              <Toaster richColors position="top-right" />
            </OrganizationProvider>
          </QueryProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
