-- Initial migration for Jummah Management System
-- This creates the complete database schema

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Multi-tenant organizations
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    logo_url TEXT,
    settings JSONB DEFAULT '{}',
    subscription_tier TEXT DEFAULT 'basic',
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enhanced user profiles
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL UNIQUE, -- Clerk user ID
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'coordinator', 'user')),
    organization_id UUID REFERENCES organizations(id),
    preferences JSONB DEFAULT '{}',
    last_active TIMESTAMPTZ DEFAULT now(),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Cities reference table
CREATE TABLE cities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    state TEXT NOT NULL,
    country TEXT NOT NULL DEFAULT 'Malaysia',
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Enhanced branches with geolocation
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    branch_code TEXT NOT NULL,
    branch_name TEXT NOT NULL,
    jip_name TEXT,
    jip_contact TEXT,
    jip_email TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city_id UUID REFERENCES cities(id),
    district TEXT,
    postal_code TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    capacity INTEGER,
    facilities TEXT[],
    prayer_times JSONB,
    contact_person JSONB,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    is_deleted BOOLEAN DEFAULT FALSE,
    UNIQUE(organization_id, branch_code)
);

-- Enhanced dhae with qualifications and availability
CREATE TABLE dhae (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    dhae_name TEXT NOT NULL,
    dhae_contact TEXT,
    dhae_email TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city_id UUID REFERENCES cities(id),
    district TEXT,
    qualifications TEXT[],
    languages TEXT[],
    specializations TEXT[],
    experience_years INTEGER,
    availability_pattern JSONB, -- Weekly availability pattern
    travel_radius INTEGER, -- km willing to travel
    rating DECIMAL(3,2) DEFAULT 0,
    total_assignments INTEGER DEFAULT 0,
    notes TEXT,
    emergency_contact JSONB,
    documents JSONB[], -- Array of document references
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'on_leave')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Enhanced scheduling with recurring patterns
CREATE TABLE jummah_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    dhae_id UUID REFERENCES dhae(id) ON DELETE SET NULL,
    branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
    schedule_date DATE NOT NULL,
    recurrence_pattern JSONB, -- For recurring schedules
    recurrence_end_date DATE,
    parent_schedule_id UUID REFERENCES jummah_schedules(id), -- For recurring series
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    estimated_attendance INTEGER,
    actual_attendance INTEGER,
    feedback_rating DECIMAL(3,2),
    feedback_notes TEXT,
    special_requirements TEXT[],
    reminders_sent JSONB DEFAULT '{}',
    travel_allowance DECIMAL(10,2),
    notes TEXT,
    created_by TEXT, -- Clerk user ID
    approved_by TEXT, -- Clerk user ID
    approved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Schedule history for audit trail
CREATE TABLE schedule_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schedule_id UUID REFERENCES jummah_schedules(id) ON DELETE CASCADE,
    action TEXT NOT NULL, -- 'created', 'updated', 'cancelled', etc.
    changes JSONB, -- What changed
    changed_by TEXT, -- Clerk user ID
    changed_at TIMESTAMPTZ DEFAULT now(),
    reason TEXT
);

-- Notifications system
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL, -- Clerk user ID
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT CHECK (type IN ('info', 'warning', 'error', 'success')),
    category TEXT, -- 'schedule', 'reminder', 'system', etc.
    related_entity_type TEXT, -- 'schedule', 'branch', 'dhae'
    related_entity_id UUID,
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes for better performance
CREATE INDEX idx_branches_organization_id ON branches(organization_id);
CREATE INDEX idx_branches_status ON branches(status) WHERE is_deleted = FALSE;
CREATE INDEX idx_dhae_organization_id ON dhae(organization_id);
CREATE INDEX idx_dhae_status ON dhae(status) WHERE is_deleted = FALSE;
CREATE INDEX idx_schedules_organization_id ON jummah_schedules(organization_id);
CREATE INDEX idx_schedules_date ON jummah_schedules(schedule_date);
CREATE INDEX idx_schedules_branch_id ON jummah_schedules(branch_id);
CREATE INDEX idx_schedules_dhae_id ON jummah_schedules(dhae_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = FALSE;

-- Row Level Security (RLS) policies
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE dhae ENABLE ROW LEVEL SECURITY;
ALTER TABLE jummah_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
