-- Row Level Security Policies for Jummah Management System

-- Helper function to get user's organization ID
CREATE OR REPLACE FUNCTION get_user_organization_id(user_id TEXT)
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT organization_id 
    FROM user_profiles 
    WHERE user_profiles.user_id = $1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organizations policies
CREATE POLICY "Users can view their organization" ON organizations
  FOR SELECT USING (id = get_user_organization_id(auth.jwt() ->> 'sub'));

CREATE POLICY "Admins can update their organization" ON organizations
  FOR UPDATE USING (
    id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager')
    )
  );

-- User profiles policies
CREATE POLICY "Users can view profiles in their organization" ON user_profiles
  FOR SELECT USING (organization_id = get_user_organization_id(auth.jwt() ->> 'sub'));

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Admins can manage profiles in their organization" ON user_profiles
  FOR ALL USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager')
    )
  );

-- Branches policies
CREATE POLICY "Users can view branches in their organization" ON branches
  FOR SELECT USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    is_deleted = false
  );

CREATE POLICY "Coordinators can manage branches" ON branches
  FOR ALL USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager', 'coordinator')
    )
  );

-- Dhae policies
CREATE POLICY "Users can view dhae in their organization" ON dhae
  FOR SELECT USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    is_deleted = false
  );

CREATE POLICY "Coordinators can manage dhae" ON dhae
  FOR ALL USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager', 'coordinator')
    )
  );

-- Schedules policies
CREATE POLICY "Users can view schedules in their organization" ON jummah_schedules
  FOR SELECT USING (organization_id = get_user_organization_id(auth.jwt() ->> 'sub'));

CREATE POLICY "Users can create schedules" ON jummah_schedules
  FOR INSERT WITH CHECK (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager', 'coordinator', 'user')
    )
  );

CREATE POLICY "Coordinators can manage schedules" ON jummah_schedules
  FOR UPDATE USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager', 'coordinator')
    )
  );

CREATE POLICY "Admins can delete schedules" ON jummah_schedules
  FOR DELETE USING (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub') AND
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_id = auth.jwt() ->> 'sub' 
      AND role IN ('admin', 'manager')
    )
  );

-- Schedule history policies
CREATE POLICY "Users can view schedule history in their organization" ON schedule_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM jummah_schedules 
      WHERE jummah_schedules.id = schedule_history.schedule_id 
      AND jummah_schedules.organization_id = get_user_organization_id(auth.jwt() ->> 'sub')
    )
  );

CREATE POLICY "System can insert schedule history" ON schedule_history
  FOR INSERT WITH CHECK (true);

-- Notifications policies
CREATE POLICY "Users can view their notifications" ON notifications
  FOR SELECT USING (
    user_id = auth.jwt() ->> 'sub' AND
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub')
  );

CREATE POLICY "Users can update their notifications" ON notifications
  FOR UPDATE USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "System can create notifications" ON notifications
  FOR INSERT WITH CHECK (
    organization_id = get_user_organization_id(auth.jwt() ->> 'sub')
  );

-- Cities are public (read-only)
CREATE POLICY "Anyone can view cities" ON cities FOR SELECT USING (true);

-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE dhae ENABLE ROW LEVEL SECURITY;
ALTER TABLE jummah_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE schedule_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
