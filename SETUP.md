# Jummah Management System - Setup Guide

This guide will help you set up the Jummah Management System with real database and authentication.

## 🚀 Quick Start (Demo Mode)

The application is currently running in **demo mode** with mock data. You can explore all features without setting up a database:

1. **<PERSON>lone and Install**
   ```bash
   git clone <your-repo>
   cd JummahNext
   npm install
   npm run dev
   ```

2. **Open Browser**
   Navigate to `http://localhost:3000` (or the port shown in terminal)

3. **Explore Features**
   - Dashboard with statistics
   - Branch management
   - Dhae (speaker/imam) management
   - Scheduling overview

## 🗄️ Database Setup (Production)

To use real data and enable full functionality, set up Supabase:

### Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create a new account or sign in
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: Jummah Management System
   - **Database Password**: Choose a strong password
   - **Region**: Select closest to your users
6. Click "Create new project"

### Step 2: Get Database Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Project API Key** (anon/public key)
   - **Service Role Key** (secret key)

### Step 3: Update Environment Variables

1. In your project root, update `.env.local`:
   ```env
   # Replace with your actual Supabase credentials
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
   ```

### Step 4: Run Database Migrations

1. In Supabase dashboard, go to **SQL Editor**
2. Create a new query
3. Copy and paste the content from `supabase/migrations/20240101000000_initial_schema.sql`
4. Click "Run" to create all tables
5. Repeat for `supabase/migrations/20240101000001_seed_data.sql` to add sample data
6. Repeat for `supabase/migrations/20240101000002_rls_policies.sql` to set up security

### Step 5: Restart Application

```bash
npm run dev
```

The application will now use real database data!

## 🔐 Authentication Setup

### Step 1: Create Clerk Account

1. Go to [clerk.com](https://clerk.com)
2. Create account and new application
3. Choose authentication methods (email, social, etc.)

### Step 2: Get Clerk Credentials

1. In Clerk dashboard, go to **API Keys**
2. Copy:
   - **Publishable Key**
   - **Secret Key**

### Step 3: Update Environment Variables

Add to your `.env.local`:
```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_xxxxx
CLERK_SECRET_KEY=sk_test_xxxxx
```

### Step 4: Configure Clerk Settings

1. In Clerk dashboard, go to **User & Authentication** → **Email, Phone, Username**
2. Enable required fields:
   - ✅ Email address (required)
   - ✅ First name (required)
   - ✅ Last name (optional)
3. Go to **Sessions** and configure session settings as needed

## 📱 Features Overview

### Current Features (Working)
- ✅ **Dashboard**: Real-time statistics and overview
- ✅ **Branch Management**: CRUD operations for prayer locations
- ✅ **Dhae Management**: Imam/speaker profiles with ratings
- ✅ **Scheduling**: Basic schedule management
- ✅ **Authentication**: User login/logout with Clerk
- ✅ **Responsive Design**: Works on desktop and mobile

### Coming Soon
- 🔄 **Form Implementation**: Add/edit forms for all entities
- 🔄 **Calendar View**: Visual calendar for scheduling
- 🔄 **Real-time Updates**: Live notifications and updates
- 🔄 **Advanced Scheduling**: Recurring patterns and AI suggestions
- 🔄 **Reporting**: Analytics and detailed reports
- 🔄 **File Upload**: Document management for dhae
- 🔄 **Geolocation**: Maps integration for branches

## 🛠️ Development

### Project Structure
```
src/
├── app/                    # Next.js App Router pages
├── components/             # Reusable UI components
├── lib/
│   ├── context/           # React context providers
│   ├── hooks/             # Custom React hooks
│   ├── services/          # Data service layer
│   ├── validations/       # Zod schemas
│   └── types/             # TypeScript definitions
├── supabase/
│   └── migrations/        # Database migrations
└── middleware.ts          # Authentication middleware
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Lint codebase

### Adding New Features

1. **Database Changes**: Add migration files in `supabase/migrations/`
2. **API Layer**: Update services in `src/lib/services/`
3. **UI Components**: Add components in `src/components/`
4. **Pages**: Add pages in `src/app/`
5. **Types**: Update types in `src/lib/types/`

## 🚨 Troubleshooting

### Common Issues

1. **"Database not set up" warnings**
   - This is normal in demo mode
   - Follow database setup steps above

2. **Authentication not working**
   - Check Clerk environment variables
   - Ensure Clerk app is configured correctly

3. **Build errors**
   - Run `npm install` to ensure all dependencies
   - Check TypeScript errors with `npm run lint`

4. **Port already in use**
   - Next.js will automatically use next available port
   - Or kill existing process: `lsof -ti:3000 | xargs kill -9`

### Getting Help

1. Check the console for error messages
2. Verify environment variables are set correctly
3. Ensure database migrations have been run
4. Check Supabase and Clerk dashboards for issues

## 📚 Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Clerk Documentation](https://clerk.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Need help?** Open an issue or check the troubleshooting section above.
