# Jummah Management System - Next.js Enhanced

A comprehensive full-stack web application to manage Jummah (Friday prayer) scheduling, including branches (locations), Dhae (speakers/imams), and their assignments. Built with Next.js 14+, TypeScript, Tailwind CSS, and Supabase.

## 🚀 Project Status

✅ **IMPLEMENTED**: Basic project structure and core features have been implemented!

### What's Working:
- ✅ Next.js 14+ with App Router
- ✅ TypeScript configuration
- ✅ Tailwind CSS with shadcn/ui components
- ✅ Clerk authentication integration
- ✅ Supabase database configuration
- ✅ Complete database schema
- ✅ Dashboard layout with sidebar navigation
- ✅ Branch management pages
- ✅ Dhae management pages
- ✅ Scheduling overview pages
- ✅ TanStack Query for data fetching
- ✅ Form validation with Zod
- ✅ Service layer for data operations
- ✅ Toast notifications with Sonner

### Next Steps:
- 🔄 Set up Supabase database & Create Migration
- 🔄 Connect real data to the UI components
- 🔄 Implement form functionality
- 🔄 Add calendar view for scheduling
- 🔄 Implement real-time features
- 🔄 Add reporting and analytics

## 🛠️ Tech Stack

### Core Framework
- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5+
- **Runtime**: Node.js 18+

### Frontend & Styling
- **Styling**: Tailwind CSS v3
- **UI Components**: shadcn/ui + Radix UI primitives
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Charts**: Recharts

### Backend & Infrastructure
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Clerk.dev
- **API Layer**: Next.js API Routes
- **File Storage**: Supabase Storage

### State Management & Data Fetching
- **Server State**: TanStack Query v5
- **Client State**: Zustand + Jotai
- **Forms**: React Hook Form + Zod
- **Notifications**: Sonner

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ installed
- npm or yarn package manager

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   - Copy `.env.example` to `.env.local`
   - Fill in your Supabase and Clerk credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
   CLERK_SECRET_KEY=your-clerk-secret-key
   ```

3. **Set up the database**
   - Create a new Supabase project
   - Run the SQL schema from `database/schema.sql` in your Supabase SQL editor

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (dashboard)/        # Dashboard route group
│   │   │   ├── dashboard/      # Dashboard pages
│   │   │   │   ├── branches/   # Branch management
│   │   │   │   ├── dhae/       # Dhae management
│   │   │   │   └── scheduling/ # Scheduling
│   │   │   └── layout.tsx      # Dashboard layout
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page
│   ├── components/             # Reusable components
│   │   ├── ui/                 # shadcn/ui components
│   │   ├── layout/             # Layout components
│   │   └── providers/          # Context providers
│   ├── lib/                    # Utilities and configurations
│   │   ├── services/           # Data service layer
│   │   ├── hooks/              # Custom React hooks
│   │   ├── validations/        # Zod schemas
│   │   ├── types/              # TypeScript types
│   │   ├── supabase.ts         # Supabase client
│   │   └── utils.ts            # Utility functions
│   └── middleware.ts           # Clerk middleware
├── database/
│   └── schema.sql              # Database schema
├── public/                     # Static assets
└── package.json
```

## 🔧 Available Scripts

- `npm run dev` — Start development server
- `npm run build` — Build for production
- `npm run start` — Start production server
- `npm run lint` — Lint codebase

## 🗄️ Database Schema

The application uses a comprehensive PostgreSQL schema with the following main tables:

- **organizations** - Multi-tenant organization management
- **user_profiles** - Extended user profiles with roles
- **branches** - Prayer locations with geolocation
- **dhae** - Imams/speakers with qualifications and availability
- **jummah_schedules** - Schedule assignments with recurring patterns
- **schedule_history** - Audit trail for schedule changes
- **notifications** - System notifications

See `database/schema.sql` for the complete schema.

## 🔐 Authentication & Authorization

The application uses Clerk for authentication with the following features:

- **Multi-factor authentication**
- **Role-based access control** (admin, manager, coordinator, user)
- **Organization-based data isolation**
- **Protected routes** with middleware

## 📱 Features Overview

### Branch Management
- Complete CRUD operations for prayer locations
- Geolocation support with maps integration
- Capacity and facility management
- Contact person details

### Dhae Management
- Imam/speaker profiles with qualifications
- Language and specialization tracking
- Availability patterns and travel radius
- Rating and assignment history

### Smart Scheduling
- Conflict detection and resolution
- Recurring schedule patterns
- Priority-based assignment
- Automated notifications

### Dashboard & Analytics
- Real-time statistics and metrics
- Visual charts and reports
- Activity tracking
- Performance insights

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy with one click

### Other Platforms
1. Ensure Node.js 18+ is available
2. Set all required environment variables
3. Run `npm run build` then `npm start`

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgements

- [Next.js](https://nextjs.org/)
- [Supabase](https://supabase.com/)
- [Clerk](https://clerk.dev/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)
