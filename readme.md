# Jummah Management System - Next.js Enhanced

A comprehensive full-stack web application to manage Ju<PERSON>h (Friday prayer) scheduling, including branches (locations), Dhae (speakers/imams), and their assignments. Built with Next.js 14+, TypeScript, Tailwind CSS, and Supabase.

## Features

- **Branch Management**: Complete CRUD operations for prayer locations with detailed information
- **Dhae Management**: Manage speakers/imams with contact details and availability
- **Smart Scheduling**: Advanced scheduling system with conflict detection and auto-suggestions
- **Calendar Integration**: Interactive calendar views with drag-and-drop functionality
- **Real-time Updates**: Live notifications and real-time schedule synchronization
- **Advanced Reporting**: Comprehensive reports with data visualization and export capabilities
- **Mobile-First Design**: Responsive PWA with offline capabilities
- **API Integration**: RESTful and GraphQL API endpoints
- **Advanced Authentication**: Multi-factor authentication with role-based permissions

## Enhanced Tech Stack

### Core Framework
- **Framework**: Next.js 14+ (App Router)
- **Language**: TypeScript 5+
- **Runtime**: Node.js 18+

### Front<PERSON> & Styling
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui + Radix UI primitives
- **Icons**: Lucide React + Heroicons
- **Animations**: Framer Motion
- **Charts & Visualization**: Recharts, Chart.js, D3.js

### Backend & Infrastructure
- **Database**: Supabase (PostgreSQL) + Prisma ORM
- **Authentication**: Clerk.dev (Universal Authentication)
- **API Layer**: Next.js API Routes + tRPC
- **Real-time**: Supabase Realtime + WebSockets
- **File Storage**: Supabase Storage
- **Search**: Supabase Full-Text Search + MeiliSearch

### State Management & Data Fetching
- **Server State**: TanStack Query v5 + SWR
- **Client State**: Zustand + Jotai
- **Forms**: React Hook Form + Zod
- **Caching**: Next.js built-in caching + Redis

### Advanced Features
- **PWA**: next-pwa plugin
- **Internationalization**: next-intl
- **Analytics**: Vercel Analytics + PostHog
- **Monitoring**: Sentry
- **Testing**: Vitest + Playwright + Testing Library
- **CI/CD**: GitHub Actions + Vercel

## Enhanced Database Schema

### Extended Tables Structure

```sql
-- Enhanced user profiles
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'coordinator', 'user')),
    organization_id UUID REFERENCES organizations(id),
    preferences JSONB DEFAULT '{}',
    last_active TIMESTAMPTZ DEFAULT now(),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Multi-tenant organizations
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    logo_url TEXT,
    settings JSONB DEFAULT '{}',
    subscription_tier TEXT DEFAULT 'basic',
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enhanced branches with geolocation
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    branch_code TEXT NOT NULL,
    branch_name TEXT NOT NULL,
    jip_name TEXT,
    jip_contact TEXT,
    jip_email TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city_id UUID REFERENCES cities(id),
    district TEXT,
    postal_code TEXT,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    capacity INTEGER,
    facilities TEXT[],
    prayer_times JSONB,
    contact_person JSONB,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    is_deleted BOOLEAN DEFAULT FALSE,
    UNIQUE(organization_id, branch_code)
);

-- Enhanced dhae with qualifications and availability
CREATE TABLE dhae (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    dhae_name TEXT NOT NULL,
    dhae_contact TEXT,
    dhae_email TEXT,
    address_line1 TEXT,
    address_line2 TEXT,
    city_id UUID REFERENCES cities(id),
    district TEXT,
    qualifications TEXT[],
    languages TEXT[],
    specializations TEXT[],
    experience_years INTEGER,
    availability_pattern JSONB, -- Weekly availability pattern
    travel_radius INTEGER, -- km willing to travel
    rating DECIMAL(3,2) DEFAULT 0,
    total_assignments INTEGER DEFAULT 0,
    notes TEXT,
    emergency_contact JSONB,
    documents JSONB[], -- Array of document references
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'on_leave')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    is_deleted BOOLEAN DEFAULT FALSE
);

-- Enhanced scheduling with recurring patterns
CREATE TABLE jummah_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    dhae_id UUID REFERENCES dhae(id) ON DELETE SET NULL,
    branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
    schedule_date DATE NOT NULL,
    recurrence_pattern JSONB, -- For recurring schedules
    recurrence_end_date DATE,
    parent_schedule_id UUID REFERENCES jummah_schedules(id), -- For recurring series
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled')),
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    estimated_attendance INTEGER,
    actual_attendance INTEGER,
    feedback_rating DECIMAL(3,2),
    feedback_notes TEXT,
    special_requirements TEXT[],
    reminders_sent JSONB DEFAULT '{}',
    travel_allowance DECIMAL(10,2),
    notes TEXT,
    created_by UUID REFERENCES auth.users(id),
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Schedule history for audit trail
CREATE TABLE schedule_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schedule_id UUID REFERENCES jummah_schedules(id) ON DELETE CASCADE,
    action TEXT NOT NULL, -- 'created', 'updated', 'cancelled', etc.
    changes JSONB, -- What changed
    changed_by UUID REFERENCES auth.users(id),
    changed_at TIMESTAMPTZ DEFAULT now(),
    reason TEXT
);

-- Notifications system
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT CHECK (type IN ('info', 'warning', 'error', 'success')),
    category TEXT, -- 'schedule', 'reminder', 'system', etc.
    related_entity_type TEXT, -- 'schedule', 'branch', 'dhae'
    related_entity_id UUID,
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Advanced reporting views
CREATE VIEW schedule_analytics AS
SELECT 
    s.*,
    b.branch_name,
    b.city_id,
    d.dhae_name,
    d.rating as dhae_rating,
    EXTRACT(MONTH FROM s.schedule_date) as schedule_month,
    EXTRACT(YEAR FROM s.schedule_date) as schedule_year,
    CASE 
        WHEN s.schedule_date < CURRENT_DATE THEN 'past'
        WHEN s.schedule_date = CURRENT_DATE THEN 'today'
        ELSE 'future'
    END as time_category
FROM jummah_schedules s
LEFT JOIN branches b ON s.branch_id = b.id
LEFT JOIN dhae d ON s.dhae_id = d.id
WHERE s.status != 'cancelled';
```

### Advanced Functions

```sql
-- Intelligent dhae recommendation
CREATE OR REPLACE FUNCTION get_recommended_dhae_for_branch(
    p_branch_id UUID,
    p_schedule_date DATE,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE (
    dhae_id UUID,
    dhae_name TEXT,
    compatibility_score DECIMAL,
    travel_distance DECIMAL,
    recent_assignments INTEGER,
    avg_rating DECIMAL
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    WITH branch_location AS (
        SELECT latitude, longitude FROM branches WHERE id = p_branch_id
    ),
    dhae_scores AS (
        SELECT 
            d.id,
            d.dhae_name,
            d.rating,
            -- Calculate compatibility score based on multiple factors
            (
                (d.rating * 0.3) + 
                (CASE WHEN d.total_assignments > 0 THEN LEAST(d.total_assignments::DECIMAL / 100, 1) * 0.2 ELSE 0 END) +
                (CASE WHEN calculate_distance(d.latitude, d.longitude, bl.latitude, bl.longitude) <= d.travel_radius 
                 THEN 0.3 ELSE 0 END) +
                (0.2) -- Base availability score
            ) * 100 as compatibility_score,
            calculate_distance(d.latitude, d.longitude, bl.latitude, bl.longitude) as distance,
            (SELECT COUNT(*) FROM jummah_schedules js 
             WHERE js.dhae_id = d.id 
             AND js.schedule_date >= CURRENT_DATE - INTERVAL '30 days') as recent_count
        FROM dhae d, branch_location bl
        WHERE d.is_deleted = FALSE 
        AND d.status = 'active'
        AND d.id NOT IN (
            SELECT js.dhae_id FROM jummah_schedules js 
            WHERE js.schedule_date = p_schedule_date 
            AND js.status NOT IN ('cancelled')
            AND js.dhae_id IS NOT NULL
        )
    )
    SELECT 
        ds.id,
        ds.dhae_name,
        ds.compatibility_score,
        ds.distance,
        ds.recent_count,
        ds.rating
    FROM dhae_scores ds
    ORDER BY ds.compatibility_score DESC, ds.distance ASC
    LIMIT p_limit;
END;
$$;

-- Generate recurring schedules
CREATE OR REPLACE FUNCTION generate_recurring_schedules(
    p_parent_schedule_id UUID,
    p_end_date DATE
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    parent_schedule jummah_schedules%ROWTYPE;
    current_date DATE;
    created_count INTEGER := 0;
BEGIN
    SELECT * INTO parent_schedule FROM jummah_schedules WHERE id = p_parent_schedule_id;
    
    IF parent_schedule.recurrence_pattern IS NULL THEN
        RETURN 0;
    END IF;
    
    current_date := parent_schedule.schedule_date + INTERVAL '7 days';
    
    WHILE current_date <= p_end_date LOOP
        INSERT INTO jummah_schedules (
            organization_id, dhae_id, branch_id, schedule_date,
            parent_schedule_id, status, priority, special_requirements,
            notes, created_by, recurrence_pattern
        ) VALUES (
            parent_schedule.organization_id, parent_schedule.dhae_id, 
            parent_schedule.branch_id, current_date,
            p_parent_schedule_id, 'scheduled', parent_schedule.priority,
            parent_schedule.special_requirements, parent_schedule.notes,
            parent_schedule.created_by, parent_schedule.recurrence_pattern
        );
        
        created_count := created_count + 1;
        current_date := current_date + INTERVAL '7 days';
    END LOOP;
    
    RETURN created_count;
END;
$$;
```

## Enhanced Project Structure (Next.js App Router)

```
/
├── app/                          # Next.js 14+ App Router
│   ├── (auth)/                   # Route groups
│   │   ├── login/
│   │   │   └── page.tsx
│   │   ├── register/
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── (dashboard)/
│   │   ├── branches/
│   │   │   ├── page.tsx
│   │   │   ├── [id]/
│   │   │   │   ├── page.tsx
│   │   │   │   └── edit/page.tsx
│   │   │   └── new/page.tsx
│   │   ├── dhae/
│   │   │   ├── page.tsx
│   │   │   ├── [id]/
│   │   │   │   ├── page.tsx
│   │   │   │   └── profile/page.tsx
│   │   │   └── new/page.tsx
│   │   ├── scheduling/
│   │   │   ├── page.tsx
│   │   │   ├── calendar/page.tsx
│   │   │   ├── assignments/page.tsx
│   │   │   └── recurring/page.tsx
│   │   ├── reports/
│   │   │   ├── page.tsx
│   │   │   ├── analytics/page.tsx
│   │   │   └── exports/page.tsx
│   │   ├── settings/
│   │   │   ├── page.tsx
│   │   │   ├── organization/page.tsx
│   │   │   ├── users/page.tsx
│   │   │   └── integrations/page.tsx
│   │   └── layout.tsx
│   ├── api/                      # API Routes
│   │   ├── auth/
│   │   │   └── [...nextauth]/route.ts
│   │   ├── trpc/
│   │   │   └── [trpc]/route.ts
│   │   ├── branches/
│   │   │   ├── route.ts
│   │   │   └── [id]/route.ts
│   │   ├── dhae/
│   │   │   ├── route.ts
│   │   │   ├── [id]/route.ts
│   │   │   └── recommendations/route.ts
│   │   ├── schedules/
│   │   │   ├── route.ts
│   │   │   ├── [id]/route.ts
│   │   │   ├── conflicts/route.ts
│   │   │   └── recurring/route.ts
│   │   ├── reports/
│   │   │   ├── route.ts
│   │   │   └── export/
│   │   │       ├── excel/route.ts
│   │   │       └── pdf/route.ts
│   │   ├── notifications/
│   │   │   ├── route.ts
│   │   │   └── [id]/read/route.ts
│   │   └── webhooks/
│   │       ├── supabase/route.ts
│   │       └── stripe/route.ts
│   ├── globals.css
│   ├── layout.tsx               # Root layout
│   ├── loading.tsx              # Global loading UI
│   ├── error.tsx                # Global error UI
│   ├── not-found.tsx            # 404 page
│   └── page.tsx                 # Home page
├── components/                   # Reusable UI components
│   ├── ui/                      # shadcn/ui components
│   │   ├── button.tsx
│   │   ├── input.tsx
│   │   ├── dialog.tsx
│   │   ├── calendar.tsx
│   │   ├── data-table/
│   │   │   ├── data-table.tsx
│   │   │   ├── data-table-toolbar.tsx
│   │   │   └── data-table-pagination.tsx
│   │   └── ...
│   ├── forms/                   # Form components
│   │   ├── branch-form.tsx
│   │   ├── dhae-form.tsx
│   │   ├── schedule-form.tsx
│   │   └── form-fields/
│   ├── layout/                  # Layout components
│   │   ├── header.tsx
│   │   ├── sidebar.tsx
│   │   ├── breadcrumbs.tsx
│   │   └── mobile-nav.tsx
│   ├── scheduling/              # Scheduling specific components
│   │   ├── calendar-view.tsx
│   │   ├── schedule-card.tsx
│   │   ├── conflict-indicator.tsx
│   │   ├── dhae-selector.tsx
│   │   └── recurring-pattern.tsx
│   ├── reports/                 # Reporting components
│   │   ├── charts/
│   │   │   ├── assignment-chart.tsx
│   │   │   ├── attendance-chart.tsx
│   │   │   └── performance-chart.tsx
│   │   ├── filters/
│   │   │   ├── date-range-filter.tsx
│   │   │   ├── branch-filter.tsx
│   │   │   └── dhae-filter.tsx
│   │   └── export-buttons.tsx
│   ├── notifications/
│   │   ├── notification-bell.tsx
│   │   ├── notification-list.tsx
│   │   └── toast-notifications.tsx
│   └── providers/
│       ├── auth-provider.tsx
│       ├── query-provider.tsx
│       ├── theme-provider.tsx
│       └── trpc-provider.tsx
├── lib/                         # Utilities and configurations
│   ├── auth/
│   │   ├── config.ts           # NextAuth configuration
│   │   └── providers.ts
│   ├── db/
│   │   ├── supabase.ts        # Supabase client
│   │   ├── prisma.ts          # Prisma client (optional)
│   │   └── migrations/
│   ├── trpc/
│   │   ├── server.ts
│   │   ├── client.ts
│   │   └── routers/
│   │       ├── branches.ts
│   │       ├── dhae.ts
│   │       ├── schedules.ts
│   │       └── reports.ts
│   ├── validations/             # Zod schemas
│   │   ├── auth.ts
│   │   ├── branch.ts
│   │   ├── dhae.ts
│   │   └── schedule.ts
│   ├── services/                # Business logic services
│   │   ├── branch-service.ts
│   │   ├── dhae-service.ts
│   │   ├── schedule-service.ts
│   │   ├── notification-service.ts
│   │   └── export-service.ts
│   ├── hooks/                   # Custom React hooks
│   │   ├── use-branches.ts
│   │   ├── use-dhae.ts
│   │   ├── use-schedules.ts
│   │   ├── use-notifications.ts
│   │   └── use-real-time.ts
│   ├── utils/
│   │   ├── date-utils.ts
│   │   ├── format-utils.ts
│   │   ├── geo-utils.ts
│   │   └── export-utils.ts
│   ├── constants/
│   │   ├── app-config.ts
│   │   ├── routes.ts
│   │   └── roles.ts
│   └── types/
│       ├── api.ts
│       ├── auth.ts
│       ├── database.ts
│       └── global.ts
├── stores/                      # State management
│   ├── auth-store.ts
│   ├── ui-store.ts
│   ├── notification-store.ts
│   └── schedule-store.ts
├── styles/                      # Global styles
│   └── globals.css
├── public/
│   ├── icons/
│   ├── images/
│   └── manifest.json           # PWA manifest
├── prisma/                     # Prisma schema (optional)
│   ├── schema.prisma
│   └── migrations/
├── tests/                      # Test files
│   ├── __mocks__/
│   ├── components/
│   ├── pages/
│   ├── api/
│   └── e2e/
├── docs/                       # Documentation
│   ├── api/
│   ├── deployment/
│   └── user-guide/
├── .env.local                  # Environment variables
├── .env.example
├── next.config.js
├── tailwind.config.ts
├── tsconfig.json
├── package.json
├── vitest.config.ts
├── playwright.config.ts
└── README.md
```

## Enhanced Features Implementation

### 1. Advanced Authentication & Authorization (Clerk)

```typescript
// app/_app.tsx or app/layout.tsx
import { ClerkProvider } from '@clerk/nextjs';

export default function App({ Component, pageProps }) {
  return (
    <ClerkProvider>
      <Component {...pageProps} />
    </ClerkProvider>
  );
}
```

- Protect routes/pages with Clerk's hooks or components, e.g.:

```typescript
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';

export default function Home() {
  return (
    <>
      <SignedIn>
        <UserButton />
        {/* Protected content here */}
      </SignedIn>
      <SignedOut>
        <SignInButton />
      </SignedOut>
    </>
  );
}
```

- See [Clerk Docs](https://clerk.com/docs/quickstarts/nextjs) for advanced usage and RBAC.

### 2. Real-time Updates with Supabase

```typescript
// lib/hooks/use-real-time.ts
import { useEffect } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { supabase } from '@/lib/db/supabase'

export function useRealTimeSchedules(organizationId: string) {
  const queryClient = useQueryClient()
  
  useEffect(() => {
    const channel = supabase
      .channel('schedule-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'jummah_schedules',
          filter: `organization_id=eq.${organizationId}`
        },
        (payload) => {
          // Invalidate and refetch schedule queries
          queryClient.invalidateQueries({ queryKey: ['schedules'] })
          
          // Show real-time notification
          if (payload.eventType === 'INSERT') {
            toast.success('New schedule created')
          } else if (payload.eventType === 'UPDATE') {
            toast.info('Schedule updated')
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [organizationId, queryClient])
}
```

### 3. Advanced Scheduling with AI Suggestions

```typescript
// lib/services/schedule-service.ts
export class ScheduleService {
  static async getIntelligentSuggestions(
    branchId: string,
    dateRange: { start: Date; end: Date },
    preferences: SchedulingPreferences
  ): Promise<ScheduleSuggestion[]> {
    const { data } = await supabase.rpc('get_intelligent_schedule_suggestions', {
      p_branch_id: branchId,
      p_start_date: dateRange.start.toISOString(),
      p_end_date: dateRange.end.toISOString(),
      p_preferences: preferences
    })
    
    return data.map(suggestion => ({
      dhaeId: suggestion.dhae_id,
      dhaeName: suggestion.dhae_name,
      confidence: suggestion.confidence_score,
      reasoning: suggestion.reasoning,
      alternativeDates: suggestion.alternative_dates
    }))
  }
  
  static async createRecurringSchedule(
    scheduleData: CreateScheduleInput,
    recurrencePattern: RecurrencePattern
  ): Promise<{ created: number; conflicts: ScheduleConflict[] }> {
    const { data } = await supabase.rpc('create_recurring_schedule', {
      p_schedule_data: scheduleData,
      p_recurrence_pattern: recurrencePattern
    })
    
    return {
      created: data.created_count,
      conflicts: data.conflicts || []
    }
  }
}
```

### 4. Advanced Analytics Dashboard

```typescript
// app/(dashboard)/reports/analytics/page.tsx
export default async function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Suspense fallback={<MetricCardSkeleton />}>
          <MetricCard
            title="Total Schedules"
            metric="schedules"
            period="month"
          />
        </Suspense>
        
        <Suspense fallback={<MetricCardSkeleton />}>
          <MetricCard
            title="Active Dhae"
            metric="dhae"
            period="month"
          />
        </Suspense>
        
        <Suspense fallback={<MetricCardSkeleton />}>
          <MetricCard
            title="Avg Attendance"
            metric="attendance"
            period="month"
          />
        </Suspense>
        
        <Suspense fallback={<MetricCardSkeleton />}>
          <MetricCard
            title="Completion Rate"
            metric="completion"
            period="month"
          />
        </Suspense>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Schedule Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<ChartSkeleton />}>
              <ScheduleTrendsChart />
            </Suspense>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Dhae Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<ChartSkeleton />}>
              <DhaePerformanceChart />
            </Suspense>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Branch Activity Heatmap</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ChartSkeleton />}>
            <BranchHeatmap />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}
```

### 5. PWA Configuration

```javascript
// next.config.js
const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  buildExcludes: [/middleware-manifest\.json$/],
  scope: '/',
  sw: 'sw.js',
  runtimeCaching: [
    {
      urlPattern: /^https:\/\/.*\.supabase\.co\/.*/i,
      handler: 'NetworkFirst',
      options: {
        cacheName: 'supabase-cache',
        expiration: {
          maxEntries: 32,
          maxAgeSeconds: 24 * 60 * 60 // 24 hours
        }
      }
    }
  ]
})

/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@prisma/client']
  },
  images: {
    domains: ['*.supabase.co'],
    formats: ['image/webp', 'image/avif']
  }
}

module.exports = withPWA(nextConfig)
```

## Enhanced Development Workflow

### 1. Environment Setup

```bash
# Install Next.js with TypeScript
npx create-next-app@latest jummah-management --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install core dependencies
npm install @supabase/supabase-js next-auth @next-auth/prisma-adapter
npm install @tanstack/react-query @hookform/resolvers zod react-hook-form
npm install @radix-ui/react-* lucide-react framer-motion
npm install zustand jotai date-fns recharts

# Install development dependencies
npm install -D prisma @types/node tsx vitest @vitejs/plugin-react
npm install -D playwright @playwright/test eslint-config-next
npm install -D next-pwa tailwindcss-animate

# Install additional tools
npm install @trpc/server @trpc/client @trpc/react-query @trpc/next
npm install sentry/
```

---

## Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/jummah-management.git
   cd jummah-management
   ```
2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```
3. **Set up environment variables**
   - Copy `.env.example` to `.env.local` and fill in your Supabase and Clerk secrets.
   - Example variables:
     ```env
     NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
     NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
     SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
     NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your-clerk-publishable-key
     CLERK_SECRET_KEY=your-clerk-secret-key
     ```
4. **Run local development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```
   App will be available at [http://localhost:3000](http://localhost:3000)

5. **Database setup (optional)**
   - Use Supabase SQL editor to run the schema in this README or use Prisma migrations if enabled.

---

## Common Scripts

- `dev` — Start development server
- `build` — Build for production
- `start` — Start production server
- `lint` — Lint codebase
- `test` — Run unit/integration tests (Vitest)
- `test:e2e` — Run end-to-end tests (Playwright)
- `prisma migrate` — Run database migrations

---

## Deployment

- **Vercel** (recommended):
  - Connect your repo and set environment variables in Vercel dashboard.
  - Deploy with one click or via CLI.
- **Other platforms:**
  - Ensure Node.js 18+ and all env variables are set.
  - Run `npm run build` then `npm start`.

---

## Contributing

1. Fork this repo and create your feature branch (`git checkout -b feature/your-feature`)
2. Commit your changes (`git commit -am 'Add new feature'`)
3. Push to the branch (`git push origin feature/your-feature`)
4. Open a Pull Request

Please follow the code style and add tests for new features.

---

## License

This project is licensed under the MIT License. See [LICENSE](./LICENSE) for details.

---

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Supabase](https://supabase.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Vercel](https://vercel.com/)
- And all open-source contributors!